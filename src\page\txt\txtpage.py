#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Text Content Page Module - Handles content display and editing functionality
"""
import os
import json
import threading
import requests
import tkinter as tk
from tkinter import messagebox, ttk
from datetime import datetime
import asyncio
import hashlib
from ...utils.data_cleaner import DataCleaner
from typing import Dict, Any
import re
import ast  # 添加ast模块用于安全解析Python字符串


class TxtPage:
    """Text page for managing and displaying content"""
    
    def __init__(self, main_controller):
        self.main_controller = main_controller
        self.root = main_controller.root
         # 初始化数据清洗器
        self.data_cleaner = DataCleaner() if DataCleaner else None
        # 不在这里设置上下文菜单，而是在setup_content_tab中设置
        
        # 通用内容处理器配置
        self.content_configs = {
            "instr": {
                "name": "开场白",
                "button_attr": "instr_button",
                "cache_field": "instr",
                "display_mode": "instr",
                "json_path": ["start", "org_text", "1"],
                "prompt_template": "根据文本生成亲切自然和强调解决痛点与避雷的可直接口播的开场白,\n课程名称中山茶亭科目三线路,返回包裹在json和中的JSON数据,文本为：{text}",
                "save_method": "_save_instr_cache",
                "progress_prefix": "生成中"
            },
            "end": {
                "name": "退场白",
                "button_attr": "end_button",
                "cache_field": "end",
                "display_mode": "end",
                "json_path": ["end", "org_text", "1"],
                "prompt_template": "根据文本生成温暖感谢和期待重聚的可直接口播的退场白，体现主播对观众的感谢和未来期待,\n课程名称中山茶亭科目三线路,返回包裹在json和中的JSON数据,文本为：{text}",
                "save_method": "_save_end_cache",
                "progress_prefix": "生成中"
            }
        }
    
    def _handle_content_data(self, content_type):
        """通用内容处理器 - 智能判断是否显示结果或开始生成"""
        config = self.content_configs.get(content_type)
        if not config:
            print(f"❌ 未找到内容类型配置: {content_type}")
            return
        try:
            # 生成缓存键
            cache_key = self.main_controller._get_ai_cache_key()
            # 检查AI优化缓存中是否存在对应字段
            if cache_key in self.main_controller.ai_optimized_data:
                ai_data = self.main_controller.ai_optimized_data[cache_key]
                content_data = ai_data.get(config["cache_field"], "")
                
                if content_data:
                    # 存在数据，询问用户是否要重新生成
                    response = messagebox.askyesno(
                        f"{config['name']}确认", 
                        f"检测到已存在{config['name']}数据。\n\n是否要重新生成并覆盖现有结果？\n\n"
                        f"• 点击「是」：重新生成{config['name']}\n"
                        f"• 点击「否」：显示现有的{config['name']}",
                        icon='question'
                    )
                    
                    if response:  # 用户选择「是」，重新生成
                        print(f"🔄 用户选择重新生成{config['name']}")
                        self._start_content_generation(content_type)
                    else:  # 用户选择「否」，显示现有结果
                        print(f"📄 用户选择显示现有{config['name']}")
                        self._show_content_result(content_type)
                    return
            
            # 没有数据，开始生成
            self._start_content_generation(content_type)
        except Exception as e:
            messagebox.showerror("错误", f"处理{config['name']}时出错:\n{str(e)}")
            print(f"处理{config['name']}失败: {e}")
    
    def _show_content_result(self, content_type):
        """通用内容结果显示器"""
        config = self.content_configs.get(content_type)
        if not config:
            return
        
        try:
            # 禁用按钮
            button = getattr(self.main_controller, config["button_attr"])
            button.configure(text="加载中...", state="disabled")
            
            # 生成缓存键
            cache_key = self.main_controller._get_ai_cache_key()
            
            # 获取数据
            if cache_key not in self.main_controller.ai_optimized_data:
                messagebox.showwarning("提示", "未找到AI优化数据")
                return
            
            ai_data = self.main_controller.ai_optimized_data[cache_key]
            content_data = ai_data.get(config["cache_field"], "")
            
            if not content_data:
                messagebox.showwarning("提示", f"{config['name']}数据为空")
                return
            
            # 设置显示模式
            self.main_controller.current_display_mode = config["display_mode"]
            print(f"✅ 设置显示模式为: {config['display_mode']}")
            
            # 标记正在加载内容
            self.main_controller.is_loading_content = True
            
            # 显示数据
            self._set_text_content(content_data)
            
            # 更新最后保存的内容
            self.main_controller.last_saved_content = content_data
            
            print(f"✅ {config['name']}显示成功: {len(content_data)} 字符")
            
        except Exception as e:
            messagebox.showerror("加载失败", f"加载{config['name']}时出错:\n{str(e)}")
            print(f"显示{config['name']}失败: {e}")
        finally:
            # 恢复按钮状态
            button = getattr(self.main_controller, config["button_attr"])
            button.configure(text=config["name"], state="normal")
            self.main_controller.is_loading_content = False
    
    def _start_content_generation(self, content_type):
        """通用内容生成启动器"""
        config = self.content_configs.get(content_type)
        if not config:
            return
        
        # 初始化提示词文件
        self.main_controller.ai_optimizer.init_prompt()
        try:
            # 禁用按钮
            button = getattr(self.main_controller, config["button_attr"])
            button.configure(text=f"{config['progress_prefix']}...", state="disabled")
            
            # 从i.json获取原始文本
            original_text = self._get_text_from_i_json(config["json_path"])
            if not original_text:
                messagebox.showerror("错误", f"未找到{config['name']}原始文本")
                button.configure(text=config["name"], state="normal")
                return
            

            # 在线程中启动生成
            def run_async():
                asyncio.run(self._content_generate_async(content_type, original_text))
            
            thread = threading.Thread(target=run_async, daemon=True)
            thread.start()
            
        except Exception as e:
            messagebox.showerror("错误", f"启动{config['name']}生成失败:\n{str(e)}")
            button = getattr(self.main_controller, config["button_attr"])
            button.configure(text=config["name"], state="normal")
    
    def _get_text_from_i_json(self, json_path):
        """从i.json获取指定路径的文本"""
        try:
            i_json_path = os.path.join(self.main_controller.config_dir, "i.json")
            if not os.path.exists(i_json_path):
                print(f"❌ 配置文件不存在: {i_json_path}")
                return None
            
            with open(i_json_path, 'r', encoding='utf-8') as f:
                i_data = json.load(f)
            
            # 根据路径获取文本
            current_data = i_data
            for i, key in enumerate(json_path):
                if key not in current_data:
                    print(f"❌ 未找到键 '{key}' 在路径 {' -> '.join(json_path[:i+1])}")
                    return None
                current_data = current_data[key]
            
            # 确保获取到的是字符串类型的数据
            if current_data is None:
                print(f"❌ 路径 {' -> '.join(json_path)} 的值为空")
                return None
            
            text_value = str(current_data).strip()
            if not text_value:
                print(f"❌ 路径 {' -> '.join(json_path)} 的文本内容为空")
                return None
            
            print(f"✅ 从i.json获取文本: {text_value[:50]}...")
            return text_value
            
        except Exception as e:
            print(f"❌ 从i.json获取文本失败: {e}")
            return None
    
    async def _content_generate_async(self, content_type, original_text):
        """通用异步内容生成器"""
        config = self.content_configs.get(content_type)
        if not config:
            return
        
        try:
            # 更新进度
            self.main_controller.root.after(0, lambda: self._update_content_progress(content_type, f"正在调用AI优化{config['name']}..."))
            
            # 获取缓存键
            cache_key = self.main_controller._get_ai_cache_key()
            
            # 调用AI插件优化 - 只传递原始文本数据
            optimized_ret = await self.main_controller.ai_optimizer.optimize_content_sync(original_text, cache_key)
            print(f"✅ 调用AI优化{config['name']}: {optimized_ret}")
            
            # 从字符串提取optimized_text
            json_data = json.loads(optimized_ret)
            optimized_text = json_data.get("optimized_text")
            print(f"✅ 调用AI优化{config['name']}: {optimized_text}")
            
            # 更新GUI
            self.main_controller.root.after(0, lambda: self._on_content_generation_complete(content_type, {
                "optimized_text": optimized_text
            }))
            
        except Exception as e:
            error_msg = str(e)
            print(f"❌ 调用AI优化{config['name']}失败: {error_msg}")
            raise e
    
    def _update_content_progress(self, content_type, message):
        """通用内容生成进度更新器"""
        config = self.content_configs.get(content_type)
        if not config:
            return
        
        button = getattr(self.main_controller, config["button_attr"])
        button.configure(text=f"{config['progress_prefix']}: {message}")
    
    def _on_content_generation_complete(self, content_type, result):
        """通用内容生成完成处理器"""
        config = self.content_configs.get(content_type)
        if not config:
            return
        
        button = getattr(self.main_controller, config["button_attr"])
        button.configure(text=config["name"], state="normal")
        
        # 保存结果
        try:
            cache_key = self.main_controller._get_ai_cache_key()
            # 确保缓存键存在
            if cache_key not in self.main_controller.ai_optimized_data:
                self.main_controller.ai_optimized_data[cache_key] = {}
            # 更新AI优化文件中的对应字段
            self.main_controller.ai_optimized_data[cache_key][config["cache_field"]] = result.get("optimized_text")
            self.main_controller._save_ai_cache(self.main_controller.ai_optimized_data[cache_key])
            self._show_content_result(content_type)
        except Exception as e:
            print(f"❌ 保存{config['name']}失败: {e}")
            messagebox.showerror("保存失败", f"{config['name']}生成成功但保存失败:\n{str(e)}")
    
    def setup_content_tab(self):
        """设置文案内容标签页"""
        content_frame = ttk.Frame(self.main_controller.notebook)
        self.main_controller.notebook.add(content_frame, text="      文案      ")
        
        # 创建主容器
        main_container = ttk.Frame(content_frame)
        main_container.pack(fill="both", expand=True, padx=20, pady=20)
        
        # URL输入区域
        url_frame = ttk.LabelFrame(main_container, text="数据源设置", padding="15")
        url_frame.pack(fill="x", pady=(0, 15))
        
        url_input_frame = ttk.Frame(url_frame)
        url_input_frame.pack(fill="x")
        
        ttk.Label(url_input_frame, text="内容URL:").pack(side="left", padx=(0, 10))
        
        # 创建URL变量并绑定变化事件
        self.main_controller.content_url_var = tk.StringVar(value="http://shipin.1jiakao.cn/site/msgs.html?id=1751&c_type=0")
        self.main_controller.content_url_var.trace("w", self.on_url_changed)
        
        url_entry = ttk.Entry(url_input_frame, textvariable=self.main_controller.content_url_var, width=50)
        url_entry.pack(side="left", fill="x", expand=True, padx=(0, 10))
        
        # 更新内容按钮
        self.main_controller.update_button = ttk.Button(url_input_frame, text="更新内容", 
                                                       command=self.update_content_data, width=12)
        self.main_controller.update_button.pack(side="left")
        
        # 操作按钮区域
        operation_frame = ttk.LabelFrame(main_container, text="内容操作", padding="15")
        operation_frame.pack(fill="x", pady=(0, 15))
        
        # 第一行按钮容器
        button_row1 = ttk.Frame(operation_frame)
        button_row1.pack(fill="x", pady=(0, 5))

        # 显示原始内容按钮
        self.main_controller.show_original_button = ttk.Button(button_row1, text="显示原始内容",
                  command=self.show_original_content, width=15)
        self.main_controller.show_original_button.pack(side="left", padx=(0, 10))

        # AI优化按钮
        self.main_controller.ai_optimize_button = ttk.Button(button_row1, text="AI优化文案",
                  command=self.handle_ai_optimization, width=15)
        self.main_controller.ai_optimize_button.pack(side="left", padx=(0, 10))

        # 推广文案按钮
        self.main_controller.adv_button = ttk.Button(button_row1, text="推广文案",
                  command=self.handle_adv_data, width=15)
        self.main_controller.adv_button.pack(side="left")

        # 第二行按钮容器
        button_row2 = ttk.Frame(operation_frame)
        button_row2.pack(fill="x", pady=(5, 0))

        # 推广文案优化按钮
        self.main_controller.adv_optimize_button = ttk.Button(button_row2, text="推广文案优化",
                  command=self.handle_adv_optimize, width=15)
        self.main_controller.adv_optimize_button.pack(side="left", padx=(0, 10))

        # 开场白按钮
        self.main_controller.instr_button = ttk.Button(button_row2, text="开场白",
                  command=lambda: self._handle_content_data("instr"), width=15)
        self.main_controller.instr_button.pack(side="left", padx=(0, 10))

        # 退场白按钮
        self.main_controller.end_button = ttk.Button(button_row2, text="退场白",
                  command=lambda: self._handle_content_data("end"), width=15)
        self.main_controller.end_button.pack(side="left", padx=(0, 10))

        # 生成主播稿按钮
        self.main_controller.zhubo_button = ttk.Button(button_row2, text="生成主播稿",
                  command=self.handle_zhubo_data, width=15)
        self.main_controller.zhubo_button.pack(side="left", padx=(0, 10))

        # TTS转换按钮 - 移动到生成主播稿之后
        self.main_controller.tts_button = ttk.Button(button_row2, text="语音转换",
                  command=self.start_tts_conversion, width=15)
        self.main_controller.tts_button.pack(side="left", padx=(0, 10))
        

        
        # 推广文案进度显示区域（默认隐藏）
        self.main_controller.adv_progress_frame = ttk.Frame(operation_frame)
        
        # 进度标签
        self.main_controller.adv_progress_label = ttk.Label(
            self.main_controller.adv_progress_frame, 
            text="准备中...", 
            font=("Arial", 9)
        )
        self.main_controller.adv_progress_label.pack(pady=(5, 2))
        
        # 进度条
        self.main_controller.adv_progress_bar = ttk.Progressbar(
            self.main_controller.adv_progress_frame,
            mode='determinate',
            length=400
        )
        self.main_controller.adv_progress_bar.pack(pady=(2, 5))
        
        # 详细状态标签
        self.main_controller.adv_status_label = ttk.Label(
            self.main_controller.adv_progress_frame,
            text="",
            font=("Arial", 8),
            foreground="gray"
        )
        self.main_controller.adv_status_label.pack()
        
        # 内容显示区域 - 分为两个区域：原始内容和优化内容
        content_display_frame = ttk.LabelFrame(main_container, text="内容编辑", padding="15")
        content_display_frame.pack(fill="both", expand=True)
        
        # 创建水平分割的主框架
        content_main_frame = ttk.Frame(content_display_frame)
        content_main_frame.pack(fill="both", expand=True)
        
        # 左侧：原始内容显示区域
        original_frame = ttk.LabelFrame(content_main_frame, text="原始内容", padding="10")
        original_frame.pack(side="left", fill="both", expand=True, padx=(0, 5))
        
        # 原始内容文本框和滚动条
        original_text_frame = ttk.Frame(original_frame)
        original_text_frame.pack(fill="both", expand=True)
        
        self.main_controller.original_content_text = tk.Text(original_text_frame, wrap=tk.WORD, font=("Arial", 10))
        original_scrollbar = ttk.Scrollbar(original_text_frame, orient="vertical", command=self.main_controller.original_content_text.yview)
        self.main_controller.original_content_text.configure(yscrollcommand=original_scrollbar.set)
        
        self.main_controller.original_content_text.pack(side="left", fill="both", expand=True)
        original_scrollbar.pack(side="right", fill="y")
        
        # 原始内容信息显示区域
        original_info_frame = ttk.Frame(original_frame)
        original_info_frame.pack(fill="x", pady=(5, 0))
        
        self.main_controller.original_char_label = ttk.Label(original_info_frame, text="字符数: 0", font=("Arial", 8))
        self.main_controller.original_char_label.pack(side="left")
        
        self.main_controller.original_duration_label = ttk.Label(original_info_frame, text="预计时长: 0秒", font=("Arial", 8))
        self.main_controller.original_duration_label.pack(side="left", padx=(20, 0))
        
        # 原始内容复制按钮
        self.main_controller.original_copy_button = ttk.Button(original_info_frame, text="复制", 
                                                              command=self._copy_original_content, width=8)
        self.main_controller.original_copy_button.pack(side="right")
        

        
        # 保持原有的content_text作为向后兼容（指向原始内容文本框）
        self.main_controller.content_text = self.main_controller.original_content_text
        
        # 绑定文本变化事件 - 为两个文本框都绑定事件
        self.main_controller.content_text.bind("<KeyRelease>", self._on_text_changed)
        self.main_controller.content_text.bind("<ButtonRelease>", self._on_text_changed)
        
        # 绑定原始内容文本框事件
        self.main_controller.original_content_text.bind("<KeyRelease>", self._on_original_text_changed)
        self.main_controller.original_content_text.bind("<ButtonRelease>", self._on_original_text_changed)
        

        
        # 初始化相关属性
        self.main_controller.is_loading_content = False
        self.main_controller.auto_save_timer = None
        self.main_controller.last_saved_content = ""
        
        # 初始化新按钮状态
        self._update_button_states()
        
        # 现在设置上下文菜单，因为content_text已经创建
        self._setup_context_menu()
        
        # 初始化内容显示区域
        self._initialize_content_displays()
        
        return content_frame
    
    def _setup_context_menu(self):
        """Setup right-click context menu"""
        self.context_menu = tk.Menu(self.root, tearoff=0)
        self.context_menu.add_command(label="Copy", command=self._copy_text)
        self.context_menu.add_command(label="Paste", command=self._paste_text)
        self.context_menu.add_command(label="Select All", command=self._select_all)
        
        # Bind right-click menu to all text widgets
        self.main_controller.content_text.bind("<Button-3>", self._show_context_menu)
        self.main_controller.original_content_text.bind("<Button-3>", self._show_context_menu)
    
    def _show_context_menu(self, event):
        """Show right-click context menu"""
        try:
            self.context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.context_menu.grab_release()
    
    def _copy_text(self):
        """Copy selected text"""
        try:
            self.main_controller.content_text.event_generate("<<Copy>>")
        except Exception as e:
            print(f"Copy text failed: {e}")
    
    def _paste_text(self):
        """Paste text"""
        try:
            self.main_controller.content_text.event_generate("<<Paste>>")
        except Exception as e:
            print(f"Paste text failed: {e}")
    
    def _select_all(self):
        """Select all text"""
        try:
            self.main_controller.content_text.tag_add(tk.SEL, "1.0", tk.END)
            self.main_controller.content_text.mark_set(tk.INSERT, "1.0")
            self.main_controller.content_text.see(tk.INSERT)
        except Exception as e:
            print(f"Select all text failed: {e}")
    
    def on_url_changed(self, *args):
        """Handle URL change event"""
        try:
            # Delay check to avoid frequent triggers
            if hasattr(self, '_url_check_timer'):
                self.root.after_cancel(self._url_check_timer)
            
            self._url_check_timer = self.root.after(500, self._on_url_changed_delayed)
        except Exception as e:
            print(f"URL change event handling exception: {e}")
    
    def _on_url_changed_delayed(self):
        """Delayed URL change handler"""
        self.display_cached_data()
        self._update_button_states()
    
    def url(self):
        """Get current URL"""
        url = self.main_controller.content_url_var.get().strip()
        if not url:
            messagebox.showerror("Error", "Please enter content URL!")
            return
        return url
    
    def update_content_data(self):
        """Update content data - fetch JSON from URL and overwrite save to original_data"""
        url = self.main_controller.content_url_var.get().strip()
        if not url:
            messagebox.showerror("Error", "Please enter content URL!")
            return
            
        # Show loading state
        self.main_controller.update_button.configure(text="Updating...", state="disabled")   
        # Fetch data in new thread
        thread = threading.Thread(target=self._fetch_data_async, args=(url,), daemon=True)
        thread.start()
        
    def _fetch_data_async(self, url):
        """Async fetch JSON data and save to original data cache（基于直播地址参数）"""
        try:
            # 检查直播地址是否已设置
            if not hasattr(self.main_controller, 'url_var') or not self.main_controller.url_var.get().strip():
                error_message = "Please set live stream URL in Home tab first"
                self.root.after(0, lambda: self._on_data_updated(error_message, False))
                return
            
            # Send HTTP request
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()
            
            # Only process JSON format
            try:
                data = response.json()
            except json.JSONDecodeError:
                error_message = "Response is not valid JSON format"
                self.root.after(0, lambda: self._on_data_updated(error_message, False))
                return
            
            # Update original data cache using cache key
            cache_data = {
                "url": url,
                "data": data,
                "timestamp": datetime.now().isoformat(),
                "status": "success"
            }
            
            # 生成缓存键并保存
            cache_key = self.main_controller._get_ai_cache_key()
            self.main_controller.cached_data[cache_key] = cache_data
            print(f"Original data updated with cache key: {cache_key}")
            
            # Update GUI display
            self.root.after(0, lambda: self._on_data_updated(data, True))
            
        except Exception as e:
            print(f"Data fetch failed: {e}")
            error_message = str(e)
            self.root.after(0, lambda: self._on_data_updated(error_message, False))
            
    def _on_data_updated(self, data, success):
        """GUI update after data update completion"""
        self.main_controller.update_button.configure(text="Update Content", state="normal")
        
        if success:
            # Format and display data
            formatted_data = json.dumps(data, indent=2, ensure_ascii=False)
            
            # Set display mode for newly fetched original data
            self.main_controller.current_display_mode = "original"
            
            self.main_controller.content_text.delete(1.0, tk.END)
            self.main_controller.content_text.insert(1.0, formatted_data)
            print("✅ 数据更新成功 (模式: original)")
        else:
            # Set display mode for error state
            self.main_controller.current_display_mode = "none"
            messagebox.showerror("Error", f"Data fetch failed:\n{data}")
    
    def display_cached_data(self):
        """Display cached data, prioritize showing original data（基于直播地址参数）"""
        # 检查文案URL是否已输入
        url = self.main_controller.content_url_var.get().strip()
        if not url:
            self._set_text_content("Please enter URL address")
            return
        
        # 检查直播地址是否已设置
        if not hasattr(self.main_controller, 'url_var') or not self.main_controller.url_var.get().strip():
            self._set_text_content("Please set live stream URL in Home tab first")
            return
        
        # Mark loading content to avoid triggering edit events
        self.main_controller.is_loading_content = True
        try:
            # 生成缓存键
            cache_key = self.main_controller._get_ai_cache_key()
            
            # Prioritize showing original data
            if cache_key in self.main_controller.cached_data:
                cache_info = self.main_controller.cached_data[cache_key]
                formatted_data = json.dumps(cache_info["data"], indent=2, ensure_ascii=False)
                
                # Set display mode for original data
                self.main_controller.current_display_mode = "original"
                
                self._set_text_content(formatted_data)
                print(f"✅ 显示原始缓存数据: {cache_info['timestamp']} (模式: original)")
            else:
                # Set display mode for no data
                self.main_controller.current_display_mode = "none"
                self._set_text_content("No cached data, click 'Update Content' button to fetch")
        except Exception as e:
            print(f"Display cached data error: {e}")
            self._set_text_content("Error loading cached data")
        finally:
            # Restore edit monitoring
            self.main_controller.is_loading_content = False
            # Update last saved content
            self.main_controller.last_saved_content = self.main_controller.content_text.get(1.0, tk.END).strip()
    
    def _set_text_content(self, content):
        """Set text box content"""
        self.main_controller.content_text.delete(1.0, tk.END)
        self.main_controller.content_text.insert(1.0, content)
    
    def _on_text_changed(self, event=None):
        """Text content change event handling"""
        # If loading content, don't handle edit events
        if self.main_controller.is_loading_content:
            return
        
        # Cancel previous timer
        if self.main_controller.auto_save_timer:
            self.root.after_cancel(self.main_controller.auto_save_timer)
        
        # Set delayed save to prevent frequent operations
        self.main_controller.auto_save_timer = self.root.after(1000, self._auto_save_edited_content)
    
    def _auto_save_edited_content(self):
        """Auto save edited content - intelligently save based on current display mode"""
        try:
            url = self.main_controller.content_url_var.get().strip()
            if not url:
                return
            
            current_content = self.main_controller.content_text.get(1.0, tk.END).strip()
            
            # If content hasn't changed, don't save
            if current_content == self.main_controller.last_saved_content:
                return
            
            # Intelligent save based on current display mode
            if self.main_controller.current_display_mode == "original":
                # Save to original_data field
                self.main_controller._save_original_cache(current_content)
                print("✅ 自动保存原始内容编辑")
            elif self.main_controller.current_display_mode == "ai_optimized":
                # Save to optimized_content field
                self.main_controller._save_ai_optimized_cache(current_content)
                print("✅ 自动保存AI优化内容编辑")
            elif self.main_controller.current_display_mode == "adv_data":
                # Save to adv_data field
                url = self.main_controller.content_url_var.get().strip()
                self.main_controller._save_adv_data_cache(url, current_content)
                print("✅ 自动保存推广文案编辑")
            elif self.main_controller.current_display_mode == "adv_optimized":
                # Save to adv_data field (optimized version)
                self.main_controller._save_adv_optimized_cache(current_content)
                print("✅ 自动保存推广文案优化编辑")
            elif self.main_controller.current_display_mode in ["instr", "end"]:
                # Save to corresponding field using generic content cache
                self.main_controller._save_content_cache(current_content, self.main_controller.current_display_mode)
            elif self.main_controller.current_display_mode == "ai_generated":
                # Save to generated_content field
                self.main_controller._save_generated_content_cache(current_content)
                print("✅ 自动保存AI生成内容编辑")
            elif self.main_controller.current_display_mode == "ai_content_optimized":
                # Save to optimized_content field (for content optimization)
                self.main_controller._save_content_optimized_cache(current_content)
                print("✅ 自动保存内容优化编辑")
            elif self.main_controller.current_display_mode == "zhubo":
                # Save to zhubo file
                if hasattr(self.main_controller, 'zhubo_processor') and self.main_controller.zhubo_processor:
                    self.main_controller.zhubo_processor.save_zhubo_cache(current_content)
                    print("✅ 自动保存主播稿编辑")
                else:
                    print("❌ 主播稿处理器未初始化")
            else:
                # Unknown mode, default to original for safety
                self.main_controller._save_original_cache(current_content)
                print("⚠️ 未知显示模式，默认保存到原始内容")
            
            # Update last saved content
            self.main_controller.last_saved_content = current_content
            
        except Exception as e:
            print(f"❌ 自动保存编辑内容失败: {e}")
    
    def show_original_content(self):
        """Display original content with enhanced error diagnosis（基于直播地址参数）"""
        print("🔍 开始显示原始内容...")
        
        # 检查文案URL是否已输入
        url = self.main_controller.content_url_var.get().strip()
        if not url:
            messagebox.showerror("错误", "请先输入URL地址！")
            print("❌ 文案URL未设置")
            return
        # 检查直播地址是否已设置
        if not hasattr(self.main_controller, 'url_var') or not self.main_controller.url_var.get().strip():
            messagebox.showerror("错误", "请先在首页设置直播地址！")
            print("❌ 直播地址未设置")
            return
        
        live_url = self.main_controller.url_var.get().strip()

        try:
            cache_key = self.main_controller._get_ai_cache_key()

            # Check if original cached data exists
            if cache_key not in self.main_controller.cached_data:
                messagebox.showwarning("提示", f"当前配置没有原始数据缓存\n\n缓存键: {cache_key}\n请先点击'更新内容'按钮获取数据")
                print(f"❌ 缓存键 '{cache_key}' 不存在于 cached_data 中")
                return
            
            # Get original data and display
            cache_info = self.main_controller.cached_data[cache_key]
  
            if "data" not in cache_info:
                messagebox.showerror("错误", "缓存数据格式错误：缺少 'data' 字段")
                print("❌ 缓存数据格式错误：缺少 'data' 字段")
                return
            
            formatted_data = json.dumps(cache_info["data"], indent=2, ensure_ascii=False)
            print(f"✅ 格式化数据完成，长度: {len(formatted_data)} 字符")
            
            # Set display mode before loading content
            self.main_controller.current_display_mode = "original"
            print("✅ 设置显示模式为: original")
            
            # Load content
            self.main_controller.is_loading_content = True
            self._set_text_content(formatted_data)
            self.main_controller.last_saved_content = formatted_data
            self.main_controller.is_loading_content = False
            
            print(f"✅ 原始内容显示成功: {cache_info['timestamp']}")
            
        except ValueError as ve:
            error_msg = f"缓存键生成失败:\n{str(ve)}\n\n请检查首页直播地址是否包含 index 和 lesson_id 参数"
            messagebox.showerror("配置错误", error_msg)
            print(f"❌ ValueError: {ve}")
        except Exception as e:
            error_msg = f"显示原始内容时出错:\n{str(e)}"
            messagebox.showerror("加载失败", error_msg)
            print(f"❌ 显示原始内容失败: {e}")
            import traceback
            traceback.print_exc()
    
    def _calculate_content_md5(self, original_data, optimized_content):
        """计算原始数据和优化内容的MD5用于变化检测 - 使用主控制器的方法"""
        return self.main_controller._calculate_content_md5(original_data, optimized_content)
    
    def handle_ai_optimization(self):
        """Handle AI optimization - intelligently determine whether to show results or start optimization（基于直播地址参数）"""
        # 检查文案URL是否已输入
        url = self.main_controller.content_url_var.get().strip()
        if not url:
            messagebox.showerror("错误", "请先输入URL地址！")
            return
        
        # 检查直播地址是否已设置
        if not hasattr(self.main_controller, 'url_var') or not self.main_controller.url_var.get().strip():
            messagebox.showerror("错误", "请先在首页设置直播地址！")
            return
        
        try:
            # 生成缓存键
            cache_key = self.main_controller._get_ai_cache_key()
            
            # Check if AI optimized results exist
            if cache_key in self.main_controller.ai_optimized_data:
                ai_data = self.main_controller.ai_optimized_data[cache_key]
                if ai_data.get("success") and ai_data.get("optimized_content"):
                    # 计算当前内容的MD5进行变化检测
                    optimized_content = ai_data.get("optimized_content", [])
                    current_md5 = self._calculate_content_md5(None, optimized_content)
                    cached_md5 = ai_data.get("optimized_md5", "")
                    
                    # 确定数据变化状态
                    if current_md5 and cached_md5:
                        if current_md5 == cached_md5:
                            change_status = "📊 数据状态：无变化"
                        else:
                            change_status = "🔄 数据状态：数据有变化"
                    else:
                        change_status = "❓ 数据状态：无法检测变化"
                    
                    # 存在AI优化结果，询问用户是否要覆盖
                    response = messagebox.askyesno(
                        "覆盖确认", 
                        f"检测到已存在AI优化结果。\n\n{change_status}\n\n是否要重新优化并覆盖现有结果？\n\n"
                        "• 点击「是」：重新调用AI进行优化\n"
                        "• 点击「否」：显示现有的优化结果",
                        icon='question'
                    )
                    
                    if response:  # 用户选择「是」，重新优化
                        print("🔄 用户选择重新优化，将覆盖现有结果")
                        self.start_ai_optimization()
                    else:  # 用户选择「否」，显示现有结果
                        print("📄 用户选择显示现有优化结果")
                        self.show_ai_optimized_result()
                    return
            
            # No valid AI optimization results, start new optimization
            self.start_ai_optimization()
        except Exception as e:
            messagebox.showerror("错误", f"处理AI优化时出错:\n{str(e)}")
            print(f"处理AI优化失败: {e}")
    
    def show_ai_optimized_result(self):
        """Display AI optimization results（基于直播地址参数）"""
        try:
            # Disable button
            self.main_controller.ai_optimize_button.configure(text="加载中...", state="disabled")
            
            # 生成缓存键
            cache_key = self.main_controller._get_ai_cache_key()
            
            # Get AI optimized data
            ai_data = self.main_controller.ai_optimized_data[cache_key]
            optimized_content = ai_data.get("optimized_content", [])
            
            if not optimized_content:
                messagebox.showwarning("提示", "未找到AI优化结果")
                return
            
            # Format and display AI optimized data
            formatted_data = json.dumps(optimized_content, indent=2, ensure_ascii=False)
            
            # Set display mode before loading content
            self.main_controller.current_display_mode = "ai_optimized"
            print("✅ 设置显示模式为: ai_optimized")
            
            # Mark loading content
            self.main_controller.is_loading_content = True
            
            # Display AI optimized data
            self._set_text_content(formatted_data)
            
            # Update last saved content
            self.main_controller.last_saved_content = formatted_data
            
            print(f"✅ AI优化结果显示成功: {len(optimized_content)} items")
            
        except Exception as e:
            messagebox.showerror("加载失败", f"加载AI优化结果时出错:\n{str(e)}")
            print(f"显示AI优化结果失败: {e}")
        finally:
            # Restore button state and edit monitoring
            self.main_controller.ai_optimize_button.configure(text="AI优化文案", state="normal")
            self.main_controller.is_loading_content = False
    
    def start_ai_optimization(self):
        """Start AI optimization process（基于直播地址参数）"""
        try:
            # 初始化AI优化器的提示词
            self.main_controller.ai_optimizer.init_prompt("prompty")
            
            # 生成缓存键
            cache_key = self.main_controller._get_ai_cache_key()
            cached_data = self.main_controller.cached_data[cache_key]
            def run_async():
                asyncio.run(self._ai_optimize_async(cached_data, cache_key))
            
            thread = threading.Thread(target=run_async, daemon=True)
            thread.start()
            
        except Exception as e:
            messagebox.showerror("错误", f"启动AI优化失败:\n{str(e)}")
            self.main_controller.ai_optimize_button.configure(text="AI优化文案", state="normal")
       
    def _prepare_data(self, json_data: Dict[str, Any]) -> str:
        # 数据清洗
        cleaned_data = self.data_cleaner.clean_for_ai_processing(json_data) if self.data_cleaner else json_data   
        # 使用清洗后的数据
        return str(cleaned_data['data'])
     
    async def _ai_optimize_async(self, cached_data, cache_key):
        """Async AI optimization process（基于缓存键）"""
        try:
            # Update progress
            self.main_controller.root.after(0, lambda: self._update_ai_progress("正在调用DeepSeek模型..."))
            # 数据清洗
            _data = self._prepare_data(cached_data)
            # Call DeepSeek plugin for optimization
            _result = await self.main_controller.ai_optimizer.optimize_content_sync(_data, cache_key)
            result = self._build_optimization_result(_result)
            # Update GUI with results
            self.main_controller.root.after(0, lambda: self._on_ai_optimization_complete(result, cache_key))
            
        except Exception as e:
            error_msg = str(e)
            self.main_controller.root.after(0, lambda: self._on_ai_optimization_error(error_msg))

    def _build_optimization_result(self, raw_result: str) -> Dict[str, Any]:
        """构建优化结果的公共方法（增强错误处理）"""
        try:
            print("🏗️ 开始构建优化结果")
            print(f"🔍 原始结果长度: {len(raw_result)} 字符")
            
            # 处理响应，分离JSON和文本内容
            processed_result = self._process_deepseek_response(raw_result)
            
            # 检查处理结果
            if not processed_result or processed_result is False:
                error_msg = "DeepSeek响应处理失败"
                print(f"❌ {error_msg}")
                return {
                    "success": False,
                    "error": error_msg,
                    "raw_response": raw_result,
                    "timestamp": datetime.now().isoformat()
                }
            
            print(f"✅ DeepSeek响应处理成功")
            
            # 从AI优化器获取prompt_template
            prompt_template = ""
            if hasattr(self.main_controller, 'ai_optimizer') and self.main_controller.ai_optimizer:
                if hasattr(self.main_controller.ai_optimizer, 'prompt_template'):
                    prompt_template = self.main_controller.ai_optimizer.prompt_template
                    print(f"✅ 获取到提示词模板，长度: {len(prompt_template)} 字符")
            
            # 安全地获取optimized_content
            optimized_content = processed_result.get("optimized_content", "")
            if not optimized_content:
                print("⚠️ 优化内容为空")
            
            # 增加 optimized+md 字段(将返回processed_result和提示词生成的md字符串作为字段值)
            optimized_md = prompt_template + str(optimized_content)
            
            # 从AI优化器获取model_info
            model_info = {}
            if hasattr(self.main_controller, 'ai_optimizer') and self.main_controller.ai_optimizer:
                if hasattr(self.main_controller.ai_optimizer, 'api_config'):
                    api_config = self.main_controller.ai_optimizer.api_config
                    model_info = {
                        "model": api_config.get("model", "unknown"),
                        "temperature": api_config.get("temperature", 0.7)
                    }
                    print(f"✅ 获取到模型信息: {model_info['model']}")
            
            result = {
                "success": True,
                "optimized_md": optimized_md,
                "optimized_content": optimized_content,
                "deepseek_txt": processed_result.get("deepseek_txt", ""),
                "raw_response": raw_result,  # 保留原始响应用于调试
                "timestamp": datetime.now().isoformat(),
                "model_info": model_info
            }
            
            print("✅ 优化结果构建完成")
            return result
            
        except Exception as e:
            error_msg = f"构建优化结果时出现异常: {str(e)}"
            print(f"❌ {error_msg}")
            print(f"🔍 错误详情:")
            print(f"   - 原始结果长度: {len(raw_result)} 字符")
            print(f"   - 原始结果开头: {raw_result[:200]}...")
            print(f"   - 错误类型: {type(e).__name__}")
            
            return {
                "success": False,
                "error": error_msg,
                "raw_response": raw_result,
                "timestamp": datetime.now().isoformat(),
                "error_type": type(e).__name__
            }
    
    def _parse_api_response(self, raw_response: str) -> str:
        """统一解析API响应，支持标准OpenAI格式和直接内容格式
        
        Args:
            raw_response: 原始API响应字符串
            
        Returns:
            str: 提取的content内容
            
        Raises:
            ValueError: 当无法解析响应时抛出异常
        """
        try:
            print(f"🔍 开始解析API响应，长度: {len(raw_response)} 字符")
            print(f"🔍 响应开头: {raw_response[:200]}...")
            
            # 1. 首先尝试解析为标准OpenAI API响应格式
            try:
                api_response = json.loads(raw_response)
                print("✅ 成功解析为JSON格式")
                
                # 检查是否是标准OpenAI格式
                if isinstance(api_response, dict) and "choices" in api_response:
                    if (isinstance(api_response["choices"], list) and 
                        len(api_response["choices"]) > 0 and
                        "message" in api_response["choices"][0] and
                        "content" in api_response["choices"][0]["message"]):
                        
                        content = api_response["choices"][0]["message"]["content"]
                        print(f"✅ 从OpenAI格式中提取content，长度: {len(content)} 字符")
                        print(f"🔍 Content开头: {content[:100]}...")
                        return content
                
                # 如果不是标准格式，但是有效JSON，检查其他可能的字段
                if isinstance(api_response, dict):
                    # 检查是否直接包含content字段
                    if "content" in api_response:
                        content = api_response["content"]
                        print(f"✅ 从直接content字段中提取，长度: {len(content)} 字符")
                        return str(content)
                    
                    # 检查是否是其他格式的响应
                    if "data" in api_response:
                        content = api_response["data"]
                        print(f"✅ 从data字段中提取，长度: {len(str(content))} 字符")
                        return str(content)
                
                # 如果JSON格式但没有预期字段，返回整个JSON作为字符串
                print("⚠️ JSON格式但未找到标准字段，返回原始JSON字符串")
                return raw_response
                
            except json.JSONDecodeError:
                print("📄 不是JSON格式，尝试直接作为内容处理")
                # 不是JSON格式，直接返回原始内容
                return raw_response.strip()
                
        except Exception as e:
            error_msg = f"解析API响应时出现异常: {str(e)}"
            print(f"❌ {error_msg}")
            raise ValueError(error_msg)
    
    def _parse_content_data(self, content: str) -> Any:
        """解析content内容，支持多种数据格式
        
        Args:
            content: content内容字符串
            
        Returns:
            Any: 解析后的数据结构
            
        Raises:
            ValueError: 当无法解析content时抛出异常
        """
        try:
            print(f"🔍 开始解析content数据，长度: {len(content)} 字符")
            print(f"🔍 Content开头: {content[:100]}...")
            
            # 1. 尝试提取Markdown代码块中的内容
            json_pattern = r'```(?:json)?\s*\n?(.*?)\n?```'
            json_matches = re.findall(json_pattern, content, re.DOTALL | re.IGNORECASE)
            
            if json_matches:
                json_string = json_matches[0].strip()
                print(f"✅ 从代码块中提取内容，长度: {len(json_string)} 字符")
                
                # 先尝试JSON解析
                try:
                    parsed_data = json.loads(json_string)
                    print("✅ 成功解析代码块中的JSON数据")
                    return parsed_data
                except json.JSONDecodeError:
                    print("📄 代码块内容不是JSON，尝试Python表达式解析")
                    # 尝试Python表达式解析
                    try:
                        parsed_data = ast.literal_eval(json_string)
                        print("✅ 成功解析代码块中的Python表达式")
                        return parsed_data
                    except (ValueError, SyntaxError) as e:
                        print(f"❌ 代码块解析失败: {e}")
            
            # 2. 尝试直接解析为JSON
            try:
                parsed_data = json.loads(content)
                print("✅ 成功直接解析为JSON")
                return parsed_data
            except json.JSONDecodeError:
                print("📄 不是直接JSON格式")
            
            # 3. 尝试解析为Python数据结构（如列表、字典字符串表示）
            try:
                # 检查是否看起来像Python数据结构
                content_stripped = content.strip()
                if (content_stripped.startswith('[') and content_stripped.endswith(']')) or \
                   (content_stripped.startswith('{') and content_stripped.endswith('}')):
                    parsed_data = ast.literal_eval(content_stripped)
                    print("✅ 成功解析为Python数据结构")
                    return parsed_data
            except (ValueError, SyntaxError) as e:
                print(f"📄 Python数据结构解析失败: {e}")
            
            # 4. 尝试提取可能的JSON片段
            json_start_pattern = r'(\[.*\]|\{.*\})'
            json_match = re.search(json_start_pattern, content, re.DOTALL)
            if json_match:
                potential_json = json_match.group(1)
                print(f"🔍 尝试解析提取的数据片段，长度: {len(potential_json)} 字符")
                
                # 先尝试JSON解析
                try:
                    parsed_data = json.loads(potential_json)
                    print("✅ 成功解析提取的JSON片段")
                    return parsed_data
                except json.JSONDecodeError:
                    # 再尝试Python表达式解析
                    try:
                        parsed_data = ast.literal_eval(potential_json)
                        print("✅ 成功解析提取的Python表达式片段")
                        return parsed_data
                    except (ValueError, SyntaxError) as e:
                        print(f"❌ 数据片段解析失败: {e}")
            
            # 5. 如果所有解析都失败，返回原始字符串
            print("⚠️ 所有解析方法都失败，返回原始字符串")
            return content
            
        except Exception as e:
            error_msg = f"解析content数据时出现异常: {str(e)}"
            print(f"❌ {error_msg}")
            raise ValueError(error_msg)
                
    def _process_deepseek_response(self, raw_response: str) -> Dict[str, Any]:
        """处理DeepSeek响应（重构使用统一解析器）"""
        try: 
            print("🤖 开始处理DeepSeek响应")
            print(f"🔍 响应长度: {len(raw_response)} 字符")
            
            # 使用统一API响应解析器
            content = self._parse_api_response(raw_response)
            print(f"✅ 成功提取content，长度: {len(content)} 字符")
            
            # 解析content内容
            parsed_content = self._parse_content_data(content)
            print(f"✅ 成功解析content数据，类型: {type(parsed_content)}")
            
            # 生成剩余文本（移除数据结构后的文本）
            remaining_text = self._extract_remaining_text(content)
            print(f"✅ 成功提取剩余文本，长度: {len(remaining_text)} 字符")
            
            result = {
                "optimized_content": parsed_content,
                "deepseek_txt": remaining_text
            }
            
            print("✅ DeepSeek响应处理完成")
            return result
            
        except Exception as e:
            print(f"❌ 处理DeepSeek响应失败: {e}")
            # 增强错误诊断信息
            print(f"🔍 错误详情:")
            print(f"   - 原始响应长度: {len(raw_response)} 字符")
            print(f"   - 原始响应开头: {raw_response[:300]}...")
            print(f"   - 错误类型: {type(e).__name__}")
            print(f"   - 错误消息: {str(e)}")
            
            # 尝试基础错误恢复
            try:
                print("🔄 尝试基础错误恢复...")
                # 如果是简单的字符串，直接返回
                if isinstance(raw_response, str) and raw_response.strip():
                    return {
                        "optimized_content": raw_response.strip(),
                        "deepseek_txt": "解析失败，返回原始内容"
                    }
            except Exception as recovery_error:
                print(f"❌ 错误恢复也失败: {recovery_error}")
            
            return False
    
    def _extract_remaining_text(self, content: str) -> str:
        """提取移除数据结构后的剩余文本"""
        try:
            temp_text = content
            
            # 移除Markdown代码块
            temp_text = re.sub(r'```(?:json)?\s*\n?.*?\n?```', '', temp_text, flags=re.DOTALL | re.IGNORECASE)
            
            # 移除可能的JSON或Python数据结构
            temp_text = re.sub(r'(\[.*\]|\{.*\})', '', temp_text, flags=re.DOTALL)
            
            # 清理多余的空白行
            lines = [line.strip() for line in temp_text.split('\n') if line.strip()]
            remaining_text = '\n'.join(lines)
            
            return remaining_text
            
        except Exception as e:
            print(f"❌ 提取剩余文本失败: {e}")
            return content
  
    def _update_ai_progress(self, message):
        """Update AI optimization progress"""
        # Update button text to show progress
        self.main_controller.ai_optimize_button.configure(text=f"AI优化中: {message}")
    
    def _on_ai_optimization_complete(self, result, cache_key):
        """Handle AI optimization completion（基于缓存键）"""
        self.main_controller.ai_optimize_button.configure(text="AI优化文案", state="normal")
        
        if result.get("success"):
            # Save AI optimization results
            optimized_content = result.get("optimized_content", [])
            
            # 计算并保存MD5值用于变化检测
            content_md5 = self._calculate_content_md5(self.main_controller.cached_data[cache_key], optimized_content)
            
            # 保留现有的AI数据，只更新相关字段，避免覆盖其他数据
            if cache_key in self.main_controller.ai_optimized_data:
                ai_data = self.main_controller.ai_optimized_data[cache_key].copy()
            else:
                ai_data = {}
            
            # 更新AI优化相关字段
            ai_data.update({
                "original_data": self.main_controller.cached_data[cache_key],
                "optimized_content": optimized_content,
                "optimized_md5": content_md5,
                "success": True,
                "timestamp": datetime.now().isoformat(),
                "cache_key": cache_key,
                "cached_timestamp": datetime.now().isoformat()
            })
            
            self.main_controller.ai_optimized_data[cache_key] = ai_data
            self.main_controller._save_ai_cache( ai_data)
            
            # Display optimization results
            self.show_ai_optimized_result()
            
            print("AI优化成功完成")
        else:
            error_msg = result.get("error", "未知错误")
            messagebox.showerror("AI优化失败", f"AI优化失败:\n{error_msg}")
            print(f"AI优化失败: {error_msg}")
    
    def _on_ai_optimization_error(self, error_msg):
        """Handle AI optimization error"""
        self.main_controller.ai_optimize_button.configure(text="AI优化文案", state="normal")
        messagebox.showerror("错误", f"AI优化过程出错:\n{error_msg}")
        print(f"AI优化错误: {error_msg}")
    
    def start_tts_conversion(self):
        """Start TTS conversion"""
        
        # Check if AI TTS processor is initialized
        if not hasattr(self.main_controller, 'ai_tts_processor') or self.main_controller.ai_tts_processor is None:
            messagebox.showerror("错误", "AI文案TTS处理器未初始化，请检查插件安装或重启程序")
            return    
        try:
            # Disable button
            self.main_controller.tts_button.configure(text="语音转换中...", state="disabled")
            
            # Completion callback function
            def completion_callback(result):
                self.main_controller.root.after(0, lambda: self._on_tts_conversion_complete(result))
            
            # Start TTS conversion
            self.main_controller.ai_tts_processor.process_ai_tts_async(completion_callback)
            
        except Exception as e:
            messagebox.showerror("错误", f"启动语音转换失败:\n{str(e)}")
            self.main_controller.tts_button.configure(text="语音转换", state="normal")
    
    def _on_tts_conversion_complete(self, result):
        """Handle TTS conversion completion"""
        self.main_controller.tts_button.configure(text="语音转换", state="normal")
        
        if result.get("success"):
            stats = result.get("statistics", {})
            message = f"语音转换完成！\n"
            message += f"总计: {stats.get('total_texts', 0)} 条文案\n"
            message += f"缓存命中: {stats.get('cache_hit_count', 0)} 条\n"
            message += f"新转换: {stats.get('success_count', 0) - stats.get('cache_hit_count', 0)} 条"
            
            if stats.get('error_count', 0) > 0:
                message += f"\n失败: {stats.get('error_count', 0)} 条"
            
            messagebox.showinfo("语音转换完成", message)
            print("语音转换成功完成")
        else:
            error_msg = result.get("error", "未知错误")
            messagebox.showerror("语音转换失败", f"语音转换失败:\n{error_msg}")
            print(f"语音转换失败: {error_msg}")
    
    def _show_adv_progress(self):
        """显示推广文案进度"""
        try:
            if hasattr(self.main_controller, 'adv_progress_frame'):
                self.main_controller.adv_progress_frame.pack(fill="x", pady=(10, 0))
                self._update_adv_progress("初始化...", 0, "正在准备推广文案生成")
        except Exception as e:
            print(f"❌ 显示推广文案进度失败: {e}")
    
    def _hide_adv_progress(self):
        """隐藏推广文案进度"""
        try:
            if hasattr(self.main_controller, 'adv_progress_frame'):
                self.main_controller.adv_progress_frame.pack_forget()
        except Exception as e:
            print(f"❌ 隐藏推广文案进度失败: {e}")
    
    def _update_adv_progress(self, main_text, progress_value, detail_text=""):
        """更新推广文案进度
        
        Args:
            main_text: 主要进度文本
            progress_value: 进度值 (0-100)
            detail_text: 详细状态文本
        """
        try:
            # 检查组件是否存在
            if (hasattr(self.main_controller, 'adv_progress_label') and 
                hasattr(self.main_controller, 'adv_progress_bar') and 
                hasattr(self.main_controller, 'adv_status_label')):
                
                self.main_controller.adv_progress_label.config(text=main_text)
                self.main_controller.adv_progress_bar.config(value=progress_value)
                self.main_controller.adv_status_label.config(text=detail_text)
                
                # 强制更新GUI
                self.main_controller.root.update_idletasks()
            
        except Exception as e:
            print(f"❌ 更新推广文案进度失败: {e}")
    
    def handle_adv_data(self):
        """Handle promotion content - intelligently determine whether to show results or start generation（基于直播地址参数）"""
        # 检查文案URL是否已输入
        url = self.main_controller.content_url_var.get().strip()
        if not url:
            messagebox.showerror("错误", "请先输入URL地址！")
            return
        
        # 检查直播地址是否已设置
        if not hasattr(self.main_controller, 'url_var') or not self.main_controller.url_var.get().strip():
            messagebox.showerror("错误", "请先在首页设置直播地址！")
            return
        
        try:
            # 生成缓存键
            cache_key = self.main_controller._get_ai_cache_key()
            
            # Check if promotion data exists
            if cache_key in self.main_controller.adv_data:
                adv_data = self.main_controller.adv_data[cache_key]
                if adv_data:
                    # 存在推广文案数据，询问用户是否要重新生成
                    response = messagebox.askyesno(
                        "推广文案确认", 
                        f"检测到已存在推广文案数据。\n\n是否要重新生成并覆盖现有结果？\n\n"
                        "• 点击「是」：重新生成推广文案\n"
                        "• 点击「否」：显示现有的推广文案",
                        icon='question'
                    )
                    
                    if response:  # 用户选择「是」，重新生成
                        print("🔄 用户选择重新生成推广文案")
                        self.start_adv_data_generation()
                    else:  # 用户选择「否」，显示现有结果
                        print("📄 用户选择显示现有推广文案")
                        self.show_adv_data_result()
                    return
            
            # No promotion data exists, start generation
            self.start_adv_data_generation()
        except Exception as e:
            messagebox.showerror("错误", f"处理推广文案时出错:\n{str(e)}")
            print(f"处理推广文案失败: {e}")
    
    def show_adv_data_result(self):
        """Display promotion content results（基于直播地址参数）"""
        try:
            # Disable button
            self.main_controller.adv_button.configure(text="加载中...", state="disabled")
            
            # 生成缓存键
            cache_key = self.main_controller._get_ai_cache_key()
            
            # Get promotion data
            if cache_key not in self.main_controller.adv_data:
                messagebox.showwarning("提示", "未找到推广文案数据")
                return
            
            adv_data = self.main_controller.adv_data[cache_key]
            
            if not adv_data:
                messagebox.showwarning("提示", "推广文案数据为空")
                return
            
            # Format and display promotion data
            formatted_data = json.dumps(adv_data, indent=2, ensure_ascii=False)
            
            # Set display mode before loading content
            self.main_controller.current_display_mode = "adv_data"
            print("✅ 设置显示模式为: adv_data")
            
            # Mark loading content
            self.main_controller.is_loading_content = True
            
            # Display promotion data
            self._set_text_content(formatted_data)
            
            # Update last saved content
            self.main_controller.last_saved_content = formatted_data
            
            print(f"✅ 推广文案显示成功: {len(adv_data) if isinstance(adv_data, list) else 'N/A'} items")
            
        except Exception as e:
            messagebox.showerror("加载失败", f"加载推广文案时出错:\n{str(e)}")
            print(f"显示推广文案失败: {e}")
        finally:
            # Restore button state and edit monitoring
            self.main_controller.adv_button.configure(text="推广文案", state="normal")
            self.main_controller.is_loading_content = False
            # 确保进度条被隐藏（如果显示的话）
            self._hide_adv_progress()
    
    def start_adv_data_generation(self):
        """Start promotion content generation process（基于直播地址参数）"""
        # 检查文案URL是否已输入
        url = self.main_controller.content_url_var.get().strip()
        if not url:
            messagebox.showerror("错误", "请先输入URL地址！")
            return
        
        # 检查直播地址是否已设置
        if not hasattr(self.main_controller, 'url_var') or not self.main_controller.url_var.get().strip():
            messagebox.showerror("错误", "请先在首页设置直播地址！")
            return
        
        # Check if promotion processor is initialized
        if not hasattr(self.main_controller, 'adv_data_processor') or self.main_controller.adv_data_processor is None:
            messagebox.showerror("错误", "推广文案处理器未初始化，请检查插件安装或重启程序")
            return

        try:
            # 生成缓存键
            cache_key = self.main_controller._get_ai_cache_key()
            
            # 检查是否有原始数据和AI优化数据
            if cache_key not in self.main_controller.cached_data:
                messagebox.showerror("错误", "没有找到原始数据，请先点击'更新内容'按钮获取数据")
                return
            
            if cache_key not in self.main_controller.ai_optimized_data:
                messagebox.showerror("错误", "没有找到AI优化数据，请先进行AI优化")
                return
            
            ai_optimized_data = self.main_controller.ai_optimized_data[cache_key]
            if not ai_optimized_data.get("success") or not ai_optimized_data.get("optimized_content"):
                messagebox.showerror("错误", "AI优化数据无效或为空，请重新进行AI优化")
                return
            
            # Disable button and show progress
            self.main_controller.adv_button.configure(text="生成中...", state="disabled")
            
            # 显示进度界面
            self._show_adv_progress()
            
            # Completion callback function
            def completion_callback(result):
                self.main_controller.root.after(0, lambda: self._on_adv_generation_complete(result))
            
            # Progress callback function with GUI updates
            def progress_callback(message, progress=None, detail=""):
                def update_gui():
                    # 根据消息内容智能计算进度
                    if progress is not None:
                        progress_value = progress
                    elif "正在加载数据" in message:
                        progress_value = 5
                    elif "正在计算时间间隔" in message:
                        progress_value = 15
                    elif "正在生成推广文案" in message:
                        if "个)" in message:
                            # 提取总数信息
                            try:
                                import re
                                match = re.search(r'\((\d+) 个\)', message)
                                if match:
                                    total = int(match.group(1))
                                    progress_value = 25 + (total * 2)  # 基础25% + 每个2%
                                else:
                                    progress_value = 30
                            except:
                                progress_value = 30
                        else:
                            progress_value = 25
                    elif "生成推广文案" in message and "/" in message:
                        # 提取当前进度 "生成推广文案 X/Y"
                        try:
                            import re
                            match = re.search(r'(\d+)/(\d+)', message)
                            if match:
                                current = int(match.group(1))
                                total = int(match.group(2))
                                # 生成阶段占25%-85%的进度
                                generation_progress = (current / total) * 60  # 60%的进度空间
                                progress_value = 25 + generation_progress
                            else:
                                progress_value = 50
                        except:
                            progress_value = 50
                    elif "正在构建数据结构" in message:
                        progress_value = 90
                    elif "完成" in message:
                        progress_value = 100
                    else:
                        progress_value = None  # 保持当前进度
                    
                    # 更新进度显示
                    if progress_value is not None:
                        self._update_adv_progress(message, progress_value, detail)
                    else:
                        # 只更新文本，不更新进度条
                        self.main_controller.adv_progress_label.config(text=message)
                        if detail:
                            self.main_controller.adv_status_label.config(text=detail)
                        self.main_controller.root.update_idletasks()
                
                # 在主线程中更新GUI
                self.main_controller.root.after(0, update_gui)
            
            # Start promotion generation with progress callback
            self.main_controller.adv_data_processor.generate_adv_data_async(completion_callback, progress_callback)
            
        except Exception as e:
            # 隐藏进度显示
            self._hide_adv_progress()
            messagebox.showerror("错误", f"启动推广文案生成失败:\n{str(e)}")
            self.main_controller.adv_button.configure(text="推广文案", state="normal")
    
    def _on_adv_generation_complete(self, result):
        """Handle promotion content generation completion"""
        # 恢复按钮状态
        self.main_controller.adv_button.configure(text="推广文案", state="normal")
        
        # 隐藏进度显示
        self._hide_adv_progress()
        
        if result.get("success"):
            # Save promotion results
            adv_data = result.get("adv_data", [])
            stats = result.get("statistics", {})
            
            if adv_data:
                # 生成缓存键并保存
                cache_key = self.main_controller._get_ai_cache_key()
                
                # 保存到内存缓存
                self.main_controller.adv_data[cache_key] = adv_data
                
                # 更新AI优化文件中的adv_org字段
                if cache_key in self.main_controller.ai_optimized_data:
                    ai_data = self.main_controller.ai_optimized_data[cache_key].copy()
                    ai_data["adv_org"] = adv_data
                    ai_data["timestamp"] = datetime.now().isoformat()
                    
                    self.main_controller.ai_optimized_data[cache_key] = ai_data
                    self.main_controller._save_ai_cache(ai_data)
                
                # Display promotion results
                self.show_adv_data_result()
                
                message = f"推广文案生成完成！\n"
                message += f"总时间间隔: {stats.get('total_intervals', 0)} 个\n"
                message += f"符合条件: {stats.get('filtered_intervals', 0)} 个\n"
                message += f"生成文案: {stats.get('generated_promotions', 0)} 条"
                
                messagebox.showinfo("推广文案生成完成", message)
                print("推广文案生成成功完成")
            else:
                # 没有生成内容，但不是错误
                message = result.get("message", "没有符合条件的时间间隔")
                messagebox.showinfo("推广文案生成完成", message)
                print(f"推广文案生成完成: {message}")
        else:
            error_msg = result.get("error", "未知错误")
            messagebox.showerror("推广文案生成失败", f"推广文案生成失败:\n{error_msg}")
            print(f"推广文案生成失败: {error_msg}")
    
    def handle_zhubo_data(self):
        """处理主播稿数据 - 委托给主播稿处理器"""
        if hasattr(self.main_controller, 'zhubo_processor') and self.main_controller.zhubo_processor:
            self.main_controller.zhubo_processor.handle_zhubo_data()
    
    def handle_adv_optimize(self):
        """处理推广文案优化 - 智能判断是否显示现有结果或开始优化"""
        # 检查文案URL是否已输入
        url = self.main_controller.content_url_var.get().strip()
        if not url:
            messagebox.showerror("错误", "请先输入URL地址！")
            return
        
        # 检查直播地址是否已设置
        if not hasattr(self.main_controller, 'url_var') or not self.main_controller.url_var.get().strip():
            messagebox.showerror("错误", "请先在首页设置直播地址！")
            return
        
        try:
            # 生成缓存键
            cache_key = self.main_controller._get_ai_cache_key()
            
            # 检查是否存在AI优化数据中的adv_data字段
            if cache_key in self.main_controller.ai_optimized_data:
                ai_data = self.main_controller.ai_optimized_data[cache_key]
                existing_adv_data = ai_data.get("adv_data")
                
                if existing_adv_data:
                    # 存在优化后的推广文案数据，询问用户是否要重新优化
                    response = messagebox.askyesno(
                        "推广文案优化确认", 
                        f"检测到已存在优化后的推广文案数据。\n\n是否要重新优化并覆盖现有结果？\n\n"
                        "• 点击「是」：重新调用AI优化推广文案\n"
                        "• 点击「否」：显示现有的优化结果",
                        icon='question'
                    )
                    
                    if response:  # 用户选择「是」，重新优化
                        print("🔄 用户选择重新优化推广文案")
                        self.start_adv_optimize_generation()
                    else:  # 用户选择「否」，显示现有结果
                        print("📄 用户选择显示现有优化结果")
                        self.show_adv_optimize_result()
                    return
            
            # 没有优化数据，开始优化流程
            self.start_adv_optimize_generation()
            
        except Exception as e:
            messagebox.showerror("错误", f"处理推广文案优化时出错:\n{str(e)}")
            print(f"处理推广文案优化失败: {e}")
    
    def show_adv_optimize_result(self):
        """显示推广文案优化结果"""
        try:
            # 禁用按钮
            self.main_controller.adv_optimize_button.configure(text="加载中...", state="disabled")
            
            # 生成缓存键
            cache_key = self.main_controller._get_ai_cache_key()
            
            # 获取优化后的推广文案数据
            if cache_key not in self.main_controller.ai_optimized_data:
                messagebox.showwarning("提示", "未找到AI优化数据")
                return
            
            ai_data = self.main_controller.ai_optimized_data[cache_key]
            adv_data = ai_data.get("adv_data")
            
            if not adv_data:
                messagebox.showwarning("提示", "推广文案优化数据为空")
                return
            
            # 格式化并显示优化后的推广文案数据
            formatted_data = json.dumps(adv_data, indent=2, ensure_ascii=False)
            
            # 设置显示模式
            self.main_controller.current_display_mode = "adv_optimized"
            print("✅ 设置显示模式为: adv_optimized")
            
            # 标记正在加载内容
            self.main_controller.is_loading_content = True
            
            # 显示优化数据
            self._set_text_content(formatted_data)
            
            # 更新最后保存的内容
            self.main_controller.last_saved_content = formatted_data
            
            print(f"✅ 推广文案优化结果显示成功: {len(adv_data) if isinstance(adv_data, list) else 'N/A'} items")
            
        except Exception as e:
            messagebox.showerror("加载失败", f"加载推广文案优化结果时出错:\n{str(e)}")
            print(f"显示推广文案优化结果失败: {e}")
        finally:
            # 恢复按钮状态和编辑监控
            self.main_controller.adv_optimize_button.configure(text="推广文案优化", state="normal")
            self.main_controller.is_loading_content = False
    
    def start_adv_optimize_generation(self):
        """开始推广文案优化生成流程"""
        try:
            # 检查是否存在adv_org数据
            cache_key = self.main_controller._get_ai_cache_key()
            
            if cache_key not in self.main_controller.ai_optimized_data:
                messagebox.showerror("错误", "没有找到AI优化数据，请先进行相关数据处理")
                return
            
            ai_data = self.main_controller.ai_optimized_data[cache_key]
            adv_org_data = ai_data.get("adv_org")
            
            if not adv_org_data:
                messagebox.showerror("错误", "没有找到推广文案基础数据，请先点击'推广文案'按钮生成基础数据")
                return
            
            # 禁用按钮并显示进度
            self.main_controller.adv_optimize_button.configure(text="优化中...", state="disabled")
            
            # 初始化AI优化器的提示词
            self.main_controller.ai_optimizer.init_prompt("prod_instr")
            
            # 异步启动优化流程
            def run_async():
                asyncio.run(self._adv_optimize_async(adv_org_data))
            
            thread = threading.Thread(target=run_async, daemon=True)
            thread.start()
            
        except Exception as e:
            messagebox.showerror("错误", f"启动推广文案优化失败:\n{str(e)}")
            self.main_controller.adv_optimize_button.configure(text="推广文案优化", state="normal")
            print(f"启动推广文案优化失败: {e}")
    
    async def _adv_optimize_async(self, adv_org_data):
        """异步推广文案优化处理 - 整体数据优化版本"""
        try:
            print("🤖 开始AI推广文案优化...")
            # 构建整体优化请求
            optimization_request = self._build_complete_optimization_request(adv_org_data)
            
            print(f"🚀 发送整体优化请求...")
            
            # 获取缓存键
            cache_key = self.main_controller._get_ai_cache_key()
            
            # 一次性提交整个adv_org数据给AI优化
            optimized_result = await self.main_controller.ai_optimizer.optimize_content_sync(optimization_request, cache_key)
            
            print("✅ AI整体优化完成，正在解析结果...")
            
            # 解析整体优化结果，直接获取完整的adv_data
            optimized_data = self._parse_complete_optimization_result(optimized_result, adv_org_data)
            
            # 通知主线程处理完成
            self.main_controller.root.after(0, lambda: self._on_adv_optimize_complete({
                "success": True,
                "adv_data": optimized_data,
                "statistics": {
                    "total_projects": len(adv_org_data)
                }
            }))
            
        except Exception as e:
            print(f"❌ 推广文案优化异步处理失败: {e}")
            import traceback
            traceback.print_exc()
            
            self.main_controller.root.after(0, lambda: self._on_adv_optimize_complete({
                "success": False,
                "error": f"优化处理异常: {str(e)}"
            }))
    
    def _count_empty_promotion_items(self, adv_org_data):
        """统计需要优化的推广文案占位符数量"""
        count = 0
        
        for project in adv_org_data:
            if not isinstance(project, dict) or "list" not in project:
                continue
            
            project_list = project["list"]
            for timestamp, content in project_list.items():
                if isinstance(content, dict) and "len" in content and "txt" in content:
                    # 这是推广文案占位符
                    if not content["txt"]:  # txt为空，需要优化
                        count += 1
        
        return count
    
    def _build_complete_optimization_request(self, adv_org_data):
        """构建整体优化请求"""
        # 将adv_org数据转换为JSON字符串
        adv_org_json = json.dumps(adv_org_data, ensure_ascii=False, indent=2)
        return adv_org_json
    
    def _parse_complete_optimization_result(self, result, fallback_data):
        """解析整体优化结果（重构使用统一解析器）"""
        try:
            print("🔍 开始解析完整优化结果")
            
            # 保存豆包返回的原始数据用于分析
            self._save_ai_raw_response(result)
            
            # 1. 首先保存原始数据到 adv_ai_data 字段
            self._save_ai_raw_data(result)
            
            # 2. 使用统一API响应解析器提取content
            content = self._parse_api_response(result)
            
            # 3. 解析content数据
            parsed_data = self._parse_content_data(content)
            
            # 4. 根据数据结构类型进行处理
            optimized_data = self._process_parsed_data(parsed_data, fallback_data)
            
            if optimized_data:
                print("✅ 成功解析完整的推广文案优化数据")
                return optimized_data
            else:
                error_msg = "无法从解析的数据中获取有效的推广文案数据"
                print(f"❌ {error_msg}")
                raise ValueError(error_msg)
                
        except Exception as e:
            error_msg = f"解析AI返回结果时出现异常: {str(e)}"
            print(f"❌ {error_msg}")
            # 增强错误诊断信息
            print(f"🔍 原始结果长度: {len(result)} 字符")
            print(f"🔍 原始结果开头: {result[:200]}...")
            raise ValueError(error_msg)
    
    def _save_ai_raw_response(self, response):
        """保存AI原始响应数据用于分析"""
        try:
            # 生成缓存键
            cache_key = self.main_controller._get_ai_cache_key()
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 保存文件路径
            debug_dir = os.path.join(self.main_controller.ai_cache_dir, "debug")
            os.makedirs(debug_dir, exist_ok=True)
            
            debug_filename = f"ai_raw_response_{cache_key}_{timestamp}.txt"
            debug_path = os.path.join(debug_dir, debug_filename)
            
            # 保存原始响应
            with open(debug_path, 'w', encoding='utf-8') as f:
                f.write(f"AI原始响应数据\n")
                f.write(f"时间: {datetime.now().isoformat()}\n")
                f.write(f"缓存键: {cache_key}\n")
                f.write(f"响应长度: {len(response)} 字符\n")
                f.write("="*50 + "\n")
                f.write(response)
            
            print(f"📝 AI原始响应已保存: {debug_filename}")
            
        except Exception as e:
            print(f"⚠️ 保存AI原始响应失败: {e}")
    
    def _save_ai_raw_data(self, raw_data):
        """保存AI原始数据到adv_ai_data字段"""
        try:
            # 生成缓存键
            cache_key = self.main_controller._get_ai_cache_key()
            
            # 确保AI优化数据存在
            if cache_key not in self.main_controller.ai_optimized_data:
                self.main_controller.ai_optimized_data[cache_key] = {}
            
            # 保存原始数据到adv_ai_data字段
            ai_data = self.main_controller.ai_optimized_data[cache_key].copy()
            ai_data["adv_ai_data"] = raw_data
            ai_data["timestamp"] = datetime.now().isoformat()
            
            # 更新内存中的数据
            self.main_controller.ai_optimized_data[cache_key] = ai_data
            
            # 保存到文件
            self.main_controller._save_ai_cache(ai_data)
            
            print(f"✅ AI原始数据已保存到adv_ai_data字段")
            
        except Exception as e:
            print(f"❌ 保存AI原始数据失败: {e}")
    
    def _extract_json_from_response(self, response):
        """从AI响应中提取JSON数据（已弃用，请使用统一解析器）
        
        注意：此方法已被新的统一解析器替代，保留仅为向后兼容
        推荐使用：_parse_api_response() + _parse_content_data()
        """
        print("⚠️ 使用了已弃用的_extract_json_from_response方法，建议使用统一解析器")
        
        try:
            # 使用新的统一解析器
            content = self._parse_api_response(response)
            parsed_data = self._parse_content_data(content)
            
            # 为了向后兼容，返回JSON字符串格式
            if isinstance(parsed_data, (dict, list)):
                return json.dumps(parsed_data, ensure_ascii=False)
            else:
                return str(parsed_data)
                
        except Exception as e:
            print(f"❌ 统一解析器失败，回退到旧方法: {e}")
            
            # 回退到旧的解析逻辑（简化版）
            try:
                json_pattern = r'```(?:json)?\s*(.*?)\s*```'
                matches = re.findall(json_pattern, response, re.DOTALL | re.IGNORECASE)
                
                if matches:
                    return matches[0].strip()
                
                # 尝试直接查找JSON结构
                json_start_pattern = r'([\[\{][\s\S]*?[\]\}])'
                matches = re.findall(json_start_pattern, response, re.DOTALL)
                
                for match in matches:
                    try:
                        json.loads(match.strip())
                        return match.strip()
                    except json.JSONDecodeError:
                        continue
                
                return None
                
            except Exception as fallback_error:
                print(f"❌ 回退解析也失败: {fallback_error}")
                return None
    
    def _process_parsed_data(self, parsed_data, fallback_data):
        """处理解析后的数据，根据类型提取推广文案数据"""
        try:
            print(f"🔍 处理解析数据，类型: {type(parsed_data)}")
            
            # 情况1: 如果直接是列表格式（标准adv_data格式）
            if isinstance(parsed_data, list) and len(parsed_data) > 0:
                print("✅ 数据是标准列表格式，直接返回")
                return parsed_data
            
            # 情况2: 如果是字典格式，检查是否包含content字段
            elif isinstance(parsed_data, dict):
                # 检查是否有adv_org->content结构
                if "adv_org" in parsed_data and isinstance(parsed_data["adv_org"], dict):
                    content = parsed_data["adv_org"].get("content")
                    if content and isinstance(content, list):
                        print("✅ 从adv_org->content中提取数据")
                        return content
                
                # 检查是否直接有content字段
                if "content" in parsed_data:
                    content = parsed_data["content"]
                    if isinstance(content, list):
                        print("✅ 从content字段中提取数据")
                        return content
                    elif isinstance(content, str):
                        print("✅ content是字符串，应用到推广文案占位符")
                        return self._apply_string_content_to_structure(content, fallback_data)
                
                # 检查是否有optimized_content字段
                if "optimized_content" in parsed_data:
                    content = parsed_data["optimized_content"]
                    if isinstance(content, list):
                        print("✅ 从optimized_content字段中提取数据")
                        return content
                    elif isinstance(content, str):
                        print("✅ optimized_content是字符串，应用到推广文案占位符")
                        return self._apply_string_content_to_structure(content, fallback_data)
                
                print("⚠️ 字典格式但未找到预期的内容字段")
                return None
            
            # 情况3: 如果是字符串（直接的推广文案内容）
            elif isinstance(parsed_data, str):
                print("✅ 数据是字符串格式，应用到推广文案占位符")
                return self._apply_string_content_to_structure(parsed_data, fallback_data)
            
            else:
                print(f"⚠️ 不支持的数据类型: {type(parsed_data)}")
                return None
                
        except Exception as e:
            print(f"❌ 处理解析数据时出现异常: {e}")
            return None
    
    def _apply_string_content_to_structure(self, content_string, fallback_data):
        """将字符串内容应用到推广文案数据结构"""
        try:
            # 如果是单个字符串，需要应用到所有空的推广文案占位符
            result_data = []
            applied_count = 0
            
            for project in fallback_data:
                if not isinstance(project, dict) or "list" not in project:
                    result_data.append(project)
                    continue
                
                project_list = project["list"]
                new_project_list = {}
                
                for timestamp, content in project_list.items():
                    if isinstance(content, dict) and "len" in content and "txt" in content:
                        if not content["txt"]:
                            # 应用字符串内容到空的推广文案占位符
                            new_project_list[timestamp] = {
                                "len": content["len"],
                                "txt": content_string
                            }
                            applied_count += 1
                            print(f"✅ 应用字符串内容到 {timestamp}")
                        else:
                            new_project_list[timestamp] = content
                    else:
                        new_project_list[timestamp] = content
                
                result_data.append({"list": new_project_list})
            
            print(f"✅ 成功应用字符串内容到 {applied_count} 个推广文案占位符")
            return result_data
            
        except Exception as e:
            print(f"❌ 应用字符串内容失败: {e}")
            return None
    
    def _on_adv_optimize_complete(self, result):
        """推广文案优化完成处理"""
        # 恢复按钮状态
        self.main_controller.adv_optimize_button.configure(text="推广文案优化", state="normal")
        
        if result.get("success"):
            # 保存优化结果
            adv_data = result.get("adv_data", [])
            stats = result.get("statistics", {})
            
            if adv_data:
                # 生成缓存键并保存
                cache_key = self.main_controller._get_ai_cache_key()
                
                # 更新AI优化文件中的adv_data字段
                if cache_key in self.main_controller.ai_optimized_data:
                    ai_data = self.main_controller.ai_optimized_data[cache_key].copy()
                    ai_data["adv_data"] = adv_data
                    ai_data["timestamp"] = datetime.now().isoformat()
                    
                    self.main_controller.ai_optimized_data[cache_key] = ai_data
                    self.main_controller._save_ai_cache(ai_data)
                
                # 显示优化结果
                self.show_adv_optimize_result()
                
                message = f"推广文案优化完成！\n"
                message += f"总项目数: {stats.get('total_projects', 0)} 个\n"
                message += f"优化项目: {stats.get('optimized_items', 0)} 个"
                
                messagebox.showinfo("推广文案优化完成", message)
                print("推广文案优化成功完成")
            else:
                # 没有生成内容，但不是错误
                message = result.get("message", "没有需要优化的推广文案")
                messagebox.showinfo("推广文案优化完成", message)
                print(f"推广文案优化完成: {message}")
        else:
            error_msg = result.get("error", "未知错误")
            messagebox.showerror("推广文案优化失败", f"推广文案优化失败:\n{error_msg}")
            print(f"推广文案优化失败: {error_msg}")
    

            # Save optimized content
            try:
                cache_key = self.main_controller._get_ai_cache_key()
                
                # Save optimized content
                self.main_controller.ai_optimized_data[cache_key]["optimized_content"] = result.get("optimized_content")
                self.main_controller.ai_optimized_data[cache_key]["optimization_timestamp"] = datetime.now().isoformat()
                self.main_controller._save_ai_cache(self.main_controller.ai_optimized_data[cache_key])
                
                # Display optimized content
                self.main_controller.current_display_mode = "ai_content_optimized"
                self.main_controller.is_loading_content = True
                self._set_text_content(result.get("optimized_content"))
                self.main_controller.is_loading_content = False
                self.main_controller.last_saved_content = result.get("optimized_content")
                
                print("✅ 内容优化完成")
                messagebox.showinfo("优化完成", "内容优化成功！")
                
            except Exception as e:
                print(f"❌ 保存优化内容失败: {e}")
                messagebox.showerror("保存失败", f"内容优化成功但保存失败:\n{str(e)}")

    
    def _update_button_states(self):
        """Update button states based on content availability"""
        try:
            # Check if live stream URL is set
            if not hasattr(self.main_controller, 'url_var') or not self.main_controller.url_var.get().strip():
                return
                    
        except Exception as e:
            print(f"❌ 更新按钮状态失败: {e}")
    
    
    def _initialize_content_displays(self):
        """Initialize content display areas"""
        try:
            # Initialize original content display
            self._update_original_content_info()
            

            
            print("✅ 内容显示区域初始化完成")
        except Exception as e:
            print(f"❌ 初始化内容显示区域失败: {e}")
    
    def _on_original_text_changed(self, event=None):
        """Handle original content text changes"""
        try:
            # Update character count and duration
            self._update_original_content_info()
            
            # Auto-save if not loading content
            if not getattr(self.main_controller, 'is_loading_content', False):
                # Cancel previous timer
                if hasattr(self.main_controller, 'original_auto_save_timer') and self.main_controller.original_auto_save_timer:
                    self.main_controller.root.after_cancel(self.main_controller.original_auto_save_timer)
                
                # Set delayed save
                self.main_controller.original_auto_save_timer = self.main_controller.root.after(1000, self._auto_save_original_content)
        except Exception as e:
            print(f"❌ 处理原始内容变化失败: {e}")
    

    
    def _update_original_content_info(self):
        """Update original content character count and duration display"""
        try:
            content = self.main_controller.original_content_text.get(1.0, tk.END).strip()
            char_count = len(content)
            
            # Calculate estimated duration (assuming 3 characters per second for Chinese)
            duration_seconds = char_count // 3 if char_count > 0 else 0
            
            # Update labels
            self.main_controller.original_char_label.config(text=f"字符数: {char_count}")
            self.main_controller.original_duration_label.config(text=f"预计时长: {duration_seconds}秒")
            
        except Exception as e:
            print(f"❌ 更新原始内容信息失败: {e}")
    

    
    def _copy_original_content(self):
        """Copy original content to clipboard"""
        try:
            content = self.main_controller.original_content_text.get(1.0, tk.END).strip()
            if content:
                self.main_controller.root.clipboard_clear()
                self.main_controller.root.clipboard_append(content)
                print("✅ 原始内容已复制到剪贴板")
                # Show brief feedback
                original_text = self.main_controller.original_copy_button.cget("text")
                self.main_controller.original_copy_button.config(text="已复制")
                self.main_controller.root.after(1000, lambda: self.main_controller.original_copy_button.config(text=original_text))
            else:
                print("⚠️ 原始内容为空，无法复制")
        except Exception as e:
            print(f"❌ 复制原始内容失败: {e}")
    

    
    def _auto_save_original_content(self):
        """Auto save original content"""
        try:
            content = self.main_controller.original_content_text.get(1.0, tk.END).strip()
            cache_key = self.main_controller._get_ai_cache_key()
            
            # Save to generated_content field in AI cache
            if cache_key not in self.main_controller.ai_optimized_data:
                self.main_controller.ai_optimized_data[cache_key] = {}
            
            self.main_controller.ai_optimized_data[cache_key]["generated_content"] = content
            self.main_controller.ai_optimized_data[cache_key]["generation_timestamp"] = datetime.now().isoformat()
            self.main_controller._save_ai_cache(self.main_controller.ai_optimized_data[cache_key])
            
            print("✅ 自动保存原始内容")
        except Exception as e:
            print(f"❌ 自动保存原始内容失败: {e}")
    

    
    def set_original_content(self, content):
        """Set original content display"""
        try:
            self.main_controller.is_loading_content = True
            self.main_controller.original_content_text.delete(1.0, tk.END)
            self.main_controller.original_content_text.insert(1.0, content)
            self._update_original_content_info()
            print(f"✅ 设置原始内容: {len(content)} 字符")
        except Exception as e:
            print(f"❌ 设置原始内容失败: {e}")
        finally:
            self.main_controller.is_loading_content = False
    

    
    def get_original_content(self):
        """Get original content"""
        try:
            return self.main_controller.original_content_text.get(1.0, tk.END).strip()
        except Exception as e:
            print(f"❌ 获取原始内容失败: {e}")
            return ""
    

    
    def test_parsing_fix(self):
        """测试豆包/DeepSeek响应解析修复效果
        
        使用实际的豆包返回数据测试新的统一解析器
        """
        print("🧪 开始测试解析修复效果")
        
        # 测试数据：从debug文件中的实际豆包响应
        test_response = '''{"choices":[{"finish_reason":"stop","index":0,"logprobs":null,"message":{"content":"[{'list': {'2.529000044': '起步操作：语音播报后立即打左转向灯，记住必须等够三秒才能动方向。现在跟我做起步检查四步法：看左后视镜确认安全，踩刹车踩离合挂一档，松手刹，盯着仪表盘确认手刹完全放下再起步。高频扣分项就在手刹没放到底，千万别大意', '4.531000137': '松刹车时注意，离合要像抚摸小猫一样轻缓抬起，找到半联动点再稳住，别让车一窜一窜的，更不能熄火。现在练习离合半联动控制，感受车身轻微震动时就是最佳点', '20.555000305': '车身走正后立刻关闭左转向灯。特别注意：如果是夜间考试，起步后先关左灯，再打开前照灯。现在做灯光复位检查，确保灯光操作准确无误'}}]","reasoning_content":"..."}}],"created":1752664586,"id":"02175266447079280fec949d38f4993f2060fab1deb137517d01a","model":"doubao-seed-1-6-250615","service_tier":"default","object":"chat.completion","usage":{"completion_tokens":4112,"prompt_tokens":2550,"total_tokens":6662,"prompt_tokens_details":{"cached_tokens":0},"completion_tokens_details":{"reasoning_tokens":1124}}}'''
        
        try:
            print("\n=== 第一阶段：API响应解析测试 ===")
            
            # 测试统一API响应解析器
            content = self._parse_api_response(test_response)
            print(f"✅ API响应解析成功")
            print(f"🔍 提取的content长度: {len(content)} 字符")
            print(f"🔍 Content开头: {content[:100]}...")
            
            print("\n=== 第二阶段：Content数据解析测试 ===")
            
            # 测试content数据解析
            parsed_data = self._parse_content_data(content)
            print(f"✅ Content数据解析成功")
            print(f"🔍 解析后的数据类型: {type(parsed_data)}")
            
            if isinstance(parsed_data, list):
                print(f"🔍 列表长度: {len(parsed_data)}")
                if len(parsed_data) > 0:
                    print(f"🔍 第一个元素类型: {type(parsed_data[0])}")
                    if isinstance(parsed_data[0], dict) and 'list' in parsed_data[0]:
                        list_data = parsed_data[0]['list']
                        print(f"🔍 第一个项目包含 {len(list_data)} 个时间戳")
                        # 显示前几个时间戳
                        for i, (timestamp, text) in enumerate(list(list_data.items())[:3]):
                            print(f"🔍   时间戳 {timestamp}: {text[:50]}...")
            
            print("\n=== 第三阶段：完整处理流程测试 ===")
            
            # 测试完整的DeepSeek响应处理
            result = self._process_deepseek_response(test_response)
            print(f"✅ DeepSeek响应处理成功")
            
            if result and result is not False:
                print(f"🔍 处理结果包含字段: {list(result.keys())}")
                print(f"🔍 optimized_content类型: {type(result.get('optimized_content'))}")
                print(f"🔍 deepseek_txt长度: {len(result.get('deepseek_txt', ''))}")
            
            print("\n=== 第四阶段：优化结果构建测试 ===")
            
            # 测试优化结果构建
            optimization_result = self._build_optimization_result(test_response)
            print(f"✅ 优化结果构建成功")
            
            if optimization_result.get("success"):
                print(f"🔍 构建结果包含字段: {list(optimization_result.keys())}")
                print(f"🔍 优化内容类型: {type(optimization_result.get('optimized_content'))}")
                
                # 验证数据完整性
                optimized_content = optimization_result.get('optimized_content')
                if isinstance(optimized_content, list) and len(optimized_content) > 0:
                    print(f"✅ 数据验证成功：包含 {len(optimized_content)} 个项目")
                    
                    # 显示解析出的部分数据作为验证
                    first_item = optimized_content[0]
                    if isinstance(first_item, dict) and 'list' in first_item:
                        first_list = first_item['list']
                        print(f"✅ 第一个项目包含 {len(first_list)} 个时间戳")
                        
                        # 检查是否包含预期的时间戳
                        expected_timestamps = ['2.529000044', '4.531000137', '20.555000305']
                        found_timestamps = [ts for ts in expected_timestamps if ts in first_list]
                        print(f"✅ 找到预期时间戳: {found_timestamps}")
                        
                        if len(found_timestamps) == len(expected_timestamps):
                            print("🎉 数据解析完全成功！所有预期的时间戳都已正确解析")
                        else:
                            print(f"⚠️ 部分时间戳缺失，预期 {len(expected_timestamps)} 个，找到 {len(found_timestamps)} 个")
                else:
                    print("❌ 数据结构验证失败")
            else:
                print(f"❌ 优化结果构建失败: {optimization_result.get('error', '未知错误')}")
            
            print("\n=== 测试总结 ===")
            print("✅ 所有测试阶段都成功完成")
            print("✅ 豆包响应解析修复验证通过")
            print("✅ 新的统一解析器工作正常")
            
            return True
            
        except Exception as e:
            print(f"\n❌ 测试过程中出现异常: {e}")
            print(f"🔍 错误类型: {type(e).__name__}")
            import traceback
            traceback.print_exc()
            return False
