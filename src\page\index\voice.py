#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
语音文件生成处理器模块 - 处理主播稿数据的语音文件生成
从主播稿数据中提取文本内容，生成对应的语音文件和zhibo缓存
"""
import os
import json
import hashlib
import threading
import asyncio
from datetime import datetime
from typing import Dict, Any, List, Optional

from src.utils.tts_hash_base import TTSHashBase


class VoiceProcessor(TTSHashBase):
    """语音文件生成处理器"""
    
    def __init__(self, main_controller):
        # 调用基类初始化方法
        super().__init__()
        self.main_controller = main_controller
        self.zhibo_cache_dir = main_controller.zhibo_cache_dir
        self.tts_plugin = main_controller.tts_plugin
        
        # 确保zhibo缓存目录存在（voice缓存目录由基类管理）
        if not os.path.exists(self.zhibo_cache_dir):
            os.makedirs(self.zhibo_cache_dir)
            print(f"✅ 创建缓存目录: {self.zhibo_cache_dir}")
    
    def generate_zhibo_voice_data(self, url: str = None):
        """生成直播语音数据并保存到zhibo缓存 - 基于主播稿数据"""
        try:
            print("🎵 开始生成直播语音数据...")
            
            # 生成缓存键
            cache_key = self.main_controller._get_ai_cache_key()
            print(f"🔑 生成的缓存键: {cache_key}")
            
            # 获取主播稿数据文件路径
            zhubo_file_path = self._get_zhubo_file_path(cache_key)
            print(f"📁 主播稿文件路径: {zhubo_file_path}")
            
            if not os.path.exists(zhubo_file_path):
                print(f"⚠️ 主播稿数据文件不存在: {zhubo_file_path}")
                print("   请先在文案页面生成主播稿数据")
                return
            
            # 读取主播稿数据
            zhubo_data = self._load_zhubo_data(zhubo_file_path)
            if not zhubo_data:
                print("❌ 主播稿数据为空或格式错误")
                return
            
            print(f"📖 成功读取主播稿数据，包含 {len(zhubo_data)} 个数据项")
            
            # 提取所有文本内容
            all_texts = self._extract_texts_from_zhubo_data(zhubo_data)
            print(f"📝 从主播稿数据中提取到 {len(all_texts)} 条文本")
            
            if len(all_texts) == 0:
                print("❌ 提取的文本内容为空，检查主播稿数据格式")
                return
            
            # 打印前几条文本用于调试
            for i, (timestamp, text) in enumerate(list(all_texts.items())[:3]):
                print(f"   📄 文本 {i+1}: {timestamp} -> {text[:50]}...")
            
            # 生成语音文件路径映射
            voice_data = self._generate_voice_path_mapping(all_texts)
            print(f"🎯 生成语音路径映射: {len(voice_data)} 条")
            
            if len(voice_data) == 0:
                print("❌ 生成的语音路径映射为空")
                return
            
            # 保存到zhibo缓存
            self._save_zhibo_voice_cache(cache_key, voice_data)
            
            print(f"✅ 直播语音数据生成完成，保存到缓存: {len(voice_data)} 条")
            
            # 检查语音文件是否存在
            existence_check = self.check_voice_files_exist(voice_data)
            missing_count = sum(1 for exists in existence_check.values() if not exists)
            
            if missing_count > 0:
                print(f"⚠️ 检测到 {missing_count} 个语音文件缺失，需要进行语音转换")
            else:
                print("✅ 所有语音文件都已存在")
            
        except Exception as e:
            print(f"❌ 生成直播语音数据失败: {e}")
            import traceback
            traceback.print_exc()
    
    def _get_zhubo_file_path(self, cache_key: str) -> str:
        """获取主播稿文件路径"""
        zhubo_filename = f"{cache_key}.json"
        return os.path.join(self.zhibo_cache_dir, zhubo_filename)
    
    def _load_zhubo_data(self, file_path: str) -> Optional[List[Dict]]:
        """加载主播稿数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if not isinstance(data, list):
                print(f"❌ 主播稿数据格式错误：期望数组，得到 {type(data)}")
                return None
            
            return data
        except Exception as e:
            print(f"❌ 加载主播稿数据失败: {e}")
            return None
    
    def _extract_texts_from_zhubo_data(self, zhubo_data: List[Dict]) -> Dict[str, str]:
        """从主播稿数据中提取所有文本内容
        
        Args:
            zhubo_data: 主播稿数据数组
            
        Returns:
            Dict[时间戳, 文本内容]
        """
        all_texts = {}
        
        try:
            for item in zhubo_data:
                if not isinstance(item, dict) or 'list' not in item:
                    continue
                
                item_list = item['list']
                if not isinstance(item_list, dict):
                    continue
                
                # 提取时间戳和文本
                for timestamp, text in item_list.items():
                    if isinstance(text, str) and text.strip():
                        all_texts[str(timestamp)] = text.strip()
            
            print(f"📝 成功提取 {len(all_texts)} 条文本内容")
            return all_texts
            
        except Exception as e:
            print(f"❌ 提取文本内容失败: {e}")
            return {}
    
    def _generate_voice_path_mapping(self, texts: Dict[str, str]) -> Dict[str, str]:
        """生成语音文件路径映射
        
        Args:
            texts: {时间戳: 文本内容}
            
        Returns:
            Dict[时间戳, 语音文件路径]
        """
        voice_data = {}
        
        for timestamp, text in texts.items():
            try:
                voice_file_path = self._generate_text_path(text)
                voice_data[timestamp] = voice_file_path     
            except Exception as e:
                print(f"❌ 生成语音路径映射失败 (时间戳: {timestamp}): {e}")
                continue
        
        return voice_data
    

    def _save_zhibo_voice_cache(self, cache_key: str, voice_data: Dict[str, str]):
        """保存语音数据到zhibo缓存
        
        Args:
            cache_key: 缓存键
            voice_data: {时间戳: 语音文件路径}
        """
        try:
            # 构建保存数据结构
            zhibo_voice_data = {
                "voice_data": voice_data,
                "timestamp": datetime.now().isoformat(),
                "cache_key": cache_key,
                "total_count": len(voice_data)
            }
            
            # 保存到zhibo语音缓存文件
            voice_cache_filename = f"voice_{cache_key}.json"
            voice_cache_path = os.path.join(self.zhibo_cache_dir, voice_cache_filename)
            
            print(f"💾 准备保存语音缓存: {voice_cache_filename}")
            print(f"   数据条目数: {len(voice_data)}")
            
            with open(voice_cache_path, 'w', encoding='utf-8') as f:
                json.dump(zhibo_voice_data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 语音缓存保存成功: {voice_cache_path}")
            
        except Exception as e:
            print(f"❌ 保存语音缓存失败: {e}")
            import traceback
            traceback.print_exc()
    
    def get_voice_cache_info(self, cache_key: str) -> Optional[Dict]:
        """获取语音缓存信息"""
        try:
            voice_cache_filename = f"voice_{cache_key}.json"
            voice_cache_path = os.path.join(self.zhibo_cache_dir, voice_cache_filename)
            
            if not os.path.exists(voice_cache_path):
                return None
            
            with open(voice_cache_path, 'r', encoding='utf-8') as f:
                return json.load(f)
                
        except Exception as e:
            print(f"❌ 获取语音缓存信息失败: {e}")
            return None
    
    def check_voice_files_exist(self, voice_data: Dict[str, str]) -> Dict[str, bool]:
        """检查语音文件是否存在
        
        Args:
            voice_data: {时间戳: 语音文件路径}
            
        Returns:
            Dict[时间戳, 是否存在]
        """
        existence_check = {}
        
        for timestamp, voice_path in voice_data.items():
            try:
                existence_check[timestamp] = os.path.exists(voice_path)      
            except Exception as e:
                print(f"❌ 检查语音文件存在性失败 (时间戳: {timestamp}): {e}")
                existence_check[timestamp] = False
        
        return existence_check
    
    async def generate_missing_voice_files_async(self, voice_data: Dict[str, str], 
                                               progress_callback: Optional[callable] = None, 
                                               completion_callback: Optional[callable] = None):
        """异步生成缺失语音文件的实现"""
        try:
            # 首先需要获取原始文本数据
            cache_key = self.main_controller._get_ai_cache_key()
            zhubo_file_path = self._get_zhubo_file_path(cache_key)
            
            if not os.path.exists(zhubo_file_path):
                raise Exception(f"主播稿数据文件不存在: {zhubo_file_path}")
            
            # 读取主播稿数据获取原始文本
            zhubo_data = self._load_zhubo_data(zhubo_file_path)
            if not zhubo_data:
                raise Exception("主播稿数据为空或格式错误")
            
            # 提取文本内容
            all_texts = self._extract_texts_from_zhubo_data(zhubo_data)
            
            # 检查哪些文件缺失
            existence_check = self.check_voice_files_exist(voice_data)
            missing_items = [(ts, path) for ts, path in voice_data.items() 
                           if not existence_check.get(ts, False)]
            if not missing_items:
                if completion_callback:
                    completion_callback({
                        "success": True,
                        "message": "所有语音文件都已存在",
                        "total_count": len(voice_data),
                        "missing_count": 0
                    })
                return
            
            total_missing = len(missing_items)
            success_count = 0
            error_count = 0
            
            for i, (timestamp, voice_path) in enumerate(missing_items, 1):
                try:
                    # 获取对应的文本内容
                    text_content = all_texts.get(timestamp, "")
                    if not text_content:
                        print(f"❌ 时间戳 {timestamp} 没有对应的文本内容")
                        error_count += 1
                        continue
                    
                    # 调用TTS插件生成语音
                    result = await self.tts_plugin.synthesize_async(text_content)
                    if result.get("success"):
                        success_count += 1
                    else:
                        error_count += 1
                        print(f"❌ 语音文件生成失败 ({timestamp}): {result.get('error', '未知错误')}")
                
                except Exception as e:
                    error_count += 1
                    print(f"❌ 处理语音文件失败 ({timestamp}): {e}")
            
            # 返回结果
            if completion_callback:
                completion_callback({
                    "success": True,
                    "total_count": len(voice_data),
                    "missing_count": total_missing,
                    "success_count": success_count,
                    "error_count": error_count,
                    "message": f"语音文件生成完成：成功 {success_count} 个，失败 {error_count} 个"
                })
        
        except Exception as e:
            print(f"❌ 生成缺失语音文件失败: {e}")
            if completion_callback:
                completion_callback({
                    "success": False,
                    "error": str(e)
                }) 