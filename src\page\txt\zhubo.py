#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主播稿处理模块 - 处理主播稿生成、显示和保存功能
"""
import os
import json
import threading
from datetime import datetime
from tkinter import messagebox
import asyncio


class ZhuboDataProcessor:
    """主播稿数据处理器"""
    
    def __init__(self, main_controller):
        self.main_controller = main_controller
        self.root = main_controller.root
    
    def handle_zhubo_data(self):
        """处理主播稿数据 - 智能判断是否显示现有内容或开始生成"""
        try:
            # 检查文案URL是否已输入
            url = self.main_controller.content_url_var.get().strip()
            if not url:
                messagebox.showerror("错误", "请先输入URL地址！")
                return
            
            # 检查直播地址是否已设置
            if not hasattr(self.main_controller, 'url_var') or not self.main_controller.url_var.get().strip():
                messagebox.showerror("错误", "请先在首页设置直播地址！")
                return
            
            # 生成缓存键
            cache_key = self.main_controller._get_ai_cache_key()
            
            # 生成主播稿文件路径
            zhubo_file_path = self._get_zhubo_file_path(cache_key)
            
            # 检查主播稿文件是否存在
            if os.path.exists(zhubo_file_path):
                # 文件存在，询问用户是否要重新生成
                response = messagebox.askyesno(
                    "主播稿确认", 
                    f"检测到已存在主播稿数据。\n\n是否要重新生成并覆盖现有结果？\n\n"
                    f"• 点击「是」：重新生成主播稿\n"
                    f"• 点击「否」：显示现有的主播稿",
                    icon='question'
                )
                
                if response:  # 用户选择「是」，重新生成
                    print("🔄 用户选择重新生成主播稿")
                    self.start_zhubo_generation()
                else:  # 用户选择「否」，显示现有结果
                    print("📄 用户选择显示现有主播稿")
                    self.show_zhubo_result()
                return
            
            # 文件不存在，开始生成
            self.start_zhubo_generation()
            
        except Exception as e:
            messagebox.showerror("错误", f"处理主播稿时出错:\n{str(e)}")
            print(f"处理主播稿失败: {e}")
    
    def _get_zhubo_file_path(self, cache_key):
        """获取主播稿文件路径"""
        filename = f"{cache_key}.json"
        return os.path.join(self.main_controller.zhibo_cache_dir, filename)
    
    def show_zhubo_result(self):
        """显示现有主播稿结果"""
        try:
            # 禁用按钮
            self.main_controller.zhubo_button.configure(text="加载中...", state="disabled")
            
            # 生成缓存键
            cache_key = self.main_controller._get_ai_cache_key()
            
            # 获取主播稿文件路径
            zhubo_file_path = self._get_zhubo_file_path(cache_key)
            
            if not os.path.exists(zhubo_file_path):
                messagebox.showwarning("提示", "主播稿文件不存在")
                return
            
            # 读取主播稿数据
            with open(zhubo_file_path, 'r', encoding='utf-8') as f:
                zhubo_data = json.load(f)
            
            # 设置显示模式
            self.main_controller.current_display_mode = "zhubo"
            print("✅ 设置显示模式为: zhubo")
            
            # 标记正在加载内容
            self.main_controller.is_loading_content = True
            
            # 显示主播稿数据（JSON格式）
            formatted_data = json.dumps(zhubo_data, indent=2, ensure_ascii=False)
            self._set_text_content(formatted_data)
            
            # 更新最后保存的内容
            self.main_controller.last_saved_content = formatted_data
            
            print(f"✅ 主播稿显示成功: {len(formatted_data)} 字符")
            
        except Exception as e:
            messagebox.showerror("加载失败", f"加载主播稿时出错:\n{str(e)}")
            print(f"显示主播稿失败: {e}")
        finally:
            # 恢复按钮状态
            self.main_controller.zhubo_button.configure(text="生成主播稿", state="normal")
            self.main_controller.is_loading_content = False
    
    def start_zhubo_generation(self):
        """开始生成主播稿"""
        try:
            # 禁用按钮
            self.main_controller.zhubo_button.configure(text="生成中...", state="disabled")
            
            # 生成缓存键
            cache_key = self.main_controller._get_ai_cache_key()
            
            # 检查AI优化数据是否存在
            if cache_key not in self.main_controller.ai_optimized_data:
                messagebox.showerror("错误", "未找到AI优化数据，请先进行AI优化")
                self.main_controller.zhubo_button.configure(text="生成主播稿", state="normal")
                return
            
            # 在线程中启动生成
            def run_async():
                asyncio.run(self._zhubo_generate_async(cache_key))
            
            thread = threading.Thread(target=run_async, daemon=True)
            thread.start()
            
        except Exception as e:
            messagebox.showerror("错误", f"启动主播稿生成失败:\n{str(e)}")
            self.main_controller.zhubo_button.configure(text="生成主播稿", state="normal")
    
    async def _zhubo_generate_async(self, cache_key):
        """异步生成主播稿"""
        try:
            # 更新进度
            self.main_controller.root.after(0, lambda: self._update_zhubo_progress("正在读取AI优化数据..."))
            
            # 读取AI优化数据
            ai_data = self.main_controller.ai_optimized_data[cache_key]
            
            # 更新进度
            self.main_controller.root.after(0, lambda: self._update_zhubo_progress("正在整理主播稿数据..."))
            
            # 生成主播稿数据
            zhubo_data = self._generate_zhubo_data(ai_data)
            
            # 更新进度
            self.main_controller.root.after(0, lambda: self._update_zhubo_progress("正在保存主播稿文件..."))
            
            # 保存主播稿文件
            self._save_zhubo_file(cache_key, zhubo_data)
            
            # 更新GUI
            self.main_controller.root.after(0, lambda: self._on_zhubo_generation_complete(zhubo_data))
            
        except Exception as e:
            error_msg = str(e)
            print(f"❌ 主播稿生成失败: {error_msg}")
            self.main_controller.root.after(0, lambda: self._on_zhubo_generation_error(error_msg))
    
    def _generate_zhubo_data(self, ai_data):
        """生成主播稿数据结构"""
        try:
            # 获取基础数据
            optimized_content = ai_data.get("optimized_content", [])
            instr_content = ai_data.get("instr", "")
            end_content = ai_data.get("end", "")
            
            # 新增：读取推广文案优化数据
            adv_data = ai_data.get("adv_data", [])
            
            # 创建主播稿数据数组
            zhubo_data = []
            
            # 1. 插入开场白到数组第一个位置
            if instr_content:
                instr_item = {
                    'list': {
                        '0': instr_content
                    }
                }
                zhubo_data.append(instr_item)
                print(f"✅ 插入开场白: {instr_content[:50]}...")
            
            # 2. 合并优化内容和推广文案，按时间戳排序
            all_content_items = []
            
            # 添加优化内容项目
            for i, item in enumerate(optimized_content):
                if isinstance(item, dict) and "list" in item:
                    # 获取每个项目的最小时间戳用于排序
                    timestamps = list(item["list"].keys())
                    if timestamps:
                        min_timestamp = min(float(ts) for ts in timestamps)
                        all_content_items.append((min_timestamp, item, "optimized"))
            
            # 添加推广文案项目 - 每个推广文案作为独立项目，并转换数据格式
            promotion_count = 0
            for i, item in enumerate(adv_data):
                if isinstance(item, dict) and "list" in item:
                    project_list = item["list"]
                    
                    # 提取每个有效的推广文案作为独立项目
                    for timestamp, content in project_list.items():
                        # 检查是否是有效的推广文案
                        if isinstance(content, dict) and "txt" in content:
                            txt_content = content["txt"]
                            if txt_content and txt_content.strip():
                                # 创建独立的推广文案项目 - 数据结构转换：从{len, txt}格式转为字符串格式
                                promotion_item = {
                                    "list": {
                                        timestamp: txt_content.strip()  # 直接使用txt内容作为字符串
                                    }
                                }
                                timestamp_float = float(timestamp)
                                all_content_items.append((timestamp_float, promotion_item, "promotion"))
                                promotion_count += 1
                                print(f"📢 插入推广文案项目: 时间戳 {timestamp_float}")
            
            # 按时间戳排序所有内容项目
            all_content_items.sort(key=lambda x: x[0])
            
            # 添加排序后的内容到主播稿
            for timestamp, item, item_type in all_content_items:
                zhubo_data.append(item)
            
            print(f"✅ 插入内容项目: {len(optimized_content)} 个优化内容 + {promotion_count} 个推广文案")
            
            # 3. 插入退场白到数组最后一个位置
            if end_content:
                end_item = {
                    'list': {
                        '2000': end_content
                    }
                }
                zhubo_data.append(end_item)
                print(f"✅ 插入退场白: {end_content[:50]}...")
            
            print(f"✅ 主播稿数据生成完成: {len(zhubo_data)} 项")
            return zhubo_data
            
        except Exception as e:
            print(f"❌ 生成主播稿数据失败: {e}")
            import traceback
            traceback.print_exc()
            raise e

    def _extract_promotion_mapping(self, adv_data):
        """提取推广文案映射 {时间戳: 推广文案内容} - 已废弃，保留兼容性"""
        promotion_mapping = {}
        
        if not isinstance(adv_data, list):
            return promotion_mapping
        
        for project in adv_data:
            if not isinstance(project, dict) or "list" not in project:
                continue
            
            project_list = project["list"]
            for timestamp, content in project_list.items():
                # 检查是否是推广文案格式（包含len和txt字段）
                if isinstance(content, dict) and "txt" in content:
                    txt_content = content["txt"]
                    # 过滤出txt不为空的数据
                    if txt_content and txt_content.strip():
                        promotion_mapping[timestamp] = txt_content.strip()
                        print(f"📢 提取推广文案: {timestamp} -> {txt_content[:30]}...")
        
        print(f"✅ 提取推广文案映射: {len(promotion_mapping)} 条")
        return promotion_mapping

    def _fill_promotion_content(self, optimized_content, promotion_mapping):
        """填充推广文案内容到优化内容中 - 已废弃，保留兼容性"""
        if not promotion_mapping:
            return optimized_content
        
        filled_count = 0
        
        for project in optimized_content:
            if not isinstance(project, dict) or "list" not in project:
                continue
            
            project_list = project["list"]
            for timestamp, content in project_list.items():
                # 检查是否是需要填充的推广文案占位符
                if isinstance(content, dict) and "txt" in content:
                    # 如果txt为空且在推广映射中找到对应的时间戳
                    if not content["txt"] and timestamp in promotion_mapping:
                        # 填充推广文案内容
                        content["txt"] = promotion_mapping[timestamp]
                        filled_count += 1
                        print(f"✅ 填充推广文案: {timestamp} -> {promotion_mapping[timestamp][:30]}...")
        
        print(f"✅ 成功填充推广文案: {filled_count} 条")
        return optimized_content
    
    def _save_zhubo_file(self, cache_key, zhubo_data):
        """保存主播稿文件"""
        try:
            # 获取文件路径
            zhubo_file_path = self._get_zhubo_file_path(cache_key)
            
            # 确保目录存在
            os.makedirs(os.path.dirname(zhubo_file_path), exist_ok=True)
            
            # 保存文件
            with open(zhubo_file_path, 'w', encoding='utf-8') as f:
                json.dump(zhubo_data, f, indent=2, ensure_ascii=False)
            
            print(f"✅ 主播稿文件保存成功: {os.path.basename(zhubo_file_path)}")
            
        except Exception as e:
            print(f"❌ 保存主播稿文件失败: {e}")
            raise e
    
    def _update_zhubo_progress(self, message):
        """更新主播稿生成进度"""
        self.main_controller.zhubo_button.configure(text=f"生成中: {message}")
    
    def _on_zhubo_generation_complete(self, zhubo_data):
        """主播稿生成完成处理"""
        try:
            # 恢复按钮状态
            self.main_controller.zhubo_button.configure(text="生成主播稿", state="normal")
            
            # 显示生成结果
            self._display_zhubo_result(zhubo_data)
            
            print(f"✅ 主播稿生成完成: {len(zhubo_data)} 项")
            
        except Exception as e:
            print(f"❌ 主播稿生成完成处理失败: {e}")
            messagebox.showerror("显示失败", f"主播稿生成成功但显示失败:\n{str(e)}")
    
    def _on_zhubo_generation_error(self, error_msg):
        """主播稿生成错误处理"""
        # 恢复按钮状态
        self.main_controller.zhubo_button.configure(text="生成主播稿", state="normal")
        
        # 显示错误信息
        messagebox.showerror("生成失败", f"主播稿生成失败:\n{error_msg}")
    
    def _display_zhubo_result(self, zhubo_data):
        """显示主播稿生成结果"""
        try:
            # 设置显示模式
            self.main_controller.current_display_mode = "zhubo"
            print("✅ 设置显示模式为: zhubo")
            
            # 标记正在加载内容
            self.main_controller.is_loading_content = True
            
            # 格式化并显示数据
            formatted_data = json.dumps(zhubo_data, indent=2, ensure_ascii=False)
            self._set_text_content(formatted_data)
            
            # 更新最后保存的内容
            self.main_controller.last_saved_content = formatted_data
            
            print(f"✅ 主播稿结果显示成功: {len(formatted_data)} 字符")
            
        except Exception as e:
            print(f"❌ 显示主播稿结果失败: {e}")
            raise e
        finally:
            # 恢复编辑监控
            self.main_controller.is_loading_content = False
    
    def _set_text_content(self, content):
        """设置文本框内容"""
        self.main_controller.content_text.delete(1.0, 'end')
        self.main_controller.content_text.insert(1.0, content)
    
    def save_zhubo_cache(self, content):
        """保存主播稿编辑内容到缓存文件"""
        try:
            # 验证JSON格式
            import json
            from tkinter import messagebox
            
            try:
                data = json.loads(content)
            except json.JSONDecodeError as e:
                # 弹框提示JSON格式错误，不再继续
                messagebox.showerror("JSON格式错误", 
                    f"保存失败：内容不是有效的JSON格式\n\n"
                    f"错误位置：第{e.lineno}行 第{e.colno}列\n"
                    f"错误信息：{str(e)}")
                return
            
            # 生成缓存键
            cache_key = self.main_controller._get_ai_cache_key()
            
            # 保存到主播稿文件
            self._save_zhubo_file(cache_key, data)
            
            print(f"✅ 保存主播稿编辑: {cache_key}")
                
        except Exception as e:
            print(f"❌ 保存主播稿缓存失败: {e}") 